# MindChat Development Guide

## 📋 Overview

This guide provides comprehensive information for developers working on MindChat, including setup instructions, coding standards, testing procedures, and contribution guidelines.

## 🚀 Quick Start

### Prerequisites
- **Python 3.9+** with pip
- **Node.js 18+** with npm
- **Git** for version control
- **VS Code** (recommended) with extensions:
  - Python
  - TypeScript and JavaScript
  - Tailwind CSS IntelliSense
  - Prettier
  - ESLint

### Local Development Setup

1. **Clone Repository**
```bash
git clone <repository-url>
cd mindchat
```

2. **Backend Setup**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Frontend Setup**
```bash
cd mindchat-frontend
npm install
```

4. **Environment Configuration**
```bash
# Create .env file in backend directory
cp backend/.env.example backend/.env
# Edit with your API keys
```

5. **Start Development Servers**
```bash
# Terminal 1 - Backend
cd backend
uvicorn main:app --reload --port 8000

# Terminal 2 - Frontend
cd mindchat-frontend
npm run dev
```

6. **Access Application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

## 🏗️ Project Structure

```
mindchat/
├── backend/                     # FastAPI backend
│   ├── api/                     # API route handlers
│   │   ├── chat.py             # Chat endpoints
│   │   └── memory.py           # Memory management
│   ├── llm_providers/          # AI provider integrations
│   │   ├── openai_compatible.py
│   │   └── mock_llm.py
│   ├── memory/                 # Memory system
│   │   ├── chromadb_interface.py
│   │   └── embedding_generator.py
│   ├── search/                 # Web search integration
│   ├── shared/                 # Shared utilities
│   │   ├── schema.py           # Pydantic models
│   │   └── config.py           # Configuration
│   ├── tests/                  # Backend tests
│   ├── requirements.txt        # Python dependencies
│   └── main.py                 # FastAPI application
├── mindchat-frontend/          # Next.js frontend
│   ├── src/
│   │   ├── app/                # App router pages
│   │   │   ├── page.tsx        # Home/chat page
│   │   │   ├── memory/         # Memory browser
│   │   │   └── layout.tsx      # Root layout
│   │   ├── components/         # React components
│   │   │   ├── ui/             # UI components
│   │   │   ├── chat-input.tsx
│   │   │   ├── chat-message.tsx
│   │   │   ├── memory-browser.tsx
│   │   │   └── client-chart.tsx
│   │   ├── lib/                # Utilities
│   │   └── hooks/              # Custom hooks
│   ├── public/                 # Static assets
│   ├── tests/                  # Frontend tests
│   ├── package.json            # Node dependencies
│   └── next.config.js          # Next.js configuration
├── docs/                       # Documentation
├── docker-compose.yml          # Docker configuration
└── README.md                   # Project overview
```

## 🎯 Development Workflow

### Git Workflow

1. **Create Feature Branch**
```bash
git checkout -b feature/your-feature-name
```

2. **Make Changes**
```bash
# Make your changes
git add .
git commit -m "feat: add new feature description"
```

3. **Push and Create PR**
```bash
git push origin feature/your-feature-name
# Create pull request on GitHub
```

### Commit Message Convention

Follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks
```

Examples:
```
feat(memory): add semantic search functionality
fix(chat): resolve message ordering issue
docs(api): update endpoint documentation
```

## 🧪 Testing

### Backend Testing

```bash
cd backend
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test file
pytest tests/test_memory.py

# Run specific test
pytest tests/test_memory.py::test_add_message
```

### Frontend Testing

```bash
cd mindchat-frontend
# Install test dependencies (already in package.json)
npm install

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- memory-browser.test.tsx
```

### Test Structure

#### Backend Tests
```python
# tests/test_memory.py
import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_get_memories():
    response = client.get("/api/memory")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

@pytest.mark.asyncio
async def test_add_memory():
    # Test memory addition
    pass
```

#### Frontend Tests
```typescript
// tests/memory-browser.test.tsx
import { render, screen } from '@testing-library/react';
import { MemoryBrowser } from '@/components/memory-browser';

describe('MemoryBrowser', () => {
  test('renders memory list', () => {
    render(<MemoryBrowser />);
    expect(screen.getByText('Loading memories...')).toBeInTheDocument();
  });
});
```

## 📝 Coding Standards

### Python (Backend)

#### Code Style
- Follow [PEP 8](https://pep8.org/)
- Use [Black](https://black.readthedocs.io/) for formatting
- Use [isort](https://pycqa.github.io/isort/) for import sorting
- Use [mypy](http://mypy-lang.org/) for type checking

```bash
# Format code
black .
isort .

# Type checking
mypy .

# Linting
flake8 .
```

#### Type Hints
```python
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

def process_messages(
    messages: List[Dict[str, Any]], 
    limit: Optional[int] = None
) -> List[str]:
    """Process messages and return content list."""
    return [msg["content"] for msg in messages[:limit]]
```

#### Error Handling
```python
from fastapi import HTTPException
import logging

logger = logging.getLogger(__name__)

async def get_memory(memory_id: str):
    try:
        memory = await fetch_memory(memory_id)
        if not memory:
            raise HTTPException(status_code=404, detail="Memory not found")
        return memory
    except Exception as e:
        logger.error(f"Error fetching memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
```

### TypeScript/React (Frontend)

#### Code Style
- Use [Prettier](https://prettier.io/) for formatting
- Use [ESLint](https://eslint.org/) for linting
- Follow [React best practices](https://react.dev/learn)

```bash
# Format code
npm run format

# Linting
npm run lint

# Type checking
npm run type-check
```

#### Component Structure
```typescript
// components/example-component.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface ExampleComponentProps {
  title: string;
  onAction?: () => void;
}

export function ExampleComponent({ title, onAction }: ExampleComponentProps) {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Effect logic
  }, []);

  const handleClick = async () => {
    setLoading(true);
    try {
      await onAction?.();
    } catch (error) {
      console.error('Action failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold">{title}</h2>
      <Button onClick={handleClick} disabled={loading}>
        {loading ? 'Loading...' : 'Action'}
      </Button>
    </div>
  );
}
```

#### State Management
```typescript
// hooks/use-memory.ts
import { useState, useEffect } from 'react';

interface Memory {
  id: string;
  content: string;
  timestamp: string;
}

export function useMemory() {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMemories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/memory');
      if (!response.ok) throw new Error('Failed to fetch');
      const data = await response.json();
      setMemories(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMemories();
  }, []);

  return { memories, loading, error, refetch: fetchMemories };
}
```

## 🔧 Development Tools

### VS Code Configuration

```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

### Pre-commit Hooks

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: \.(js|ts|tsx|json|css|md)$
```

Install pre-commit:
```bash
pip install pre-commit
pre-commit install
```

## 🐛 Debugging

### Backend Debugging

```python
# Add to main.py for development
import logging
logging.basicConfig(level=logging.DEBUG)

# Use debugger
import pdb; pdb.set_trace()

# Or with ipdb (better interface)
import ipdb; ipdb.set_trace()
```

### Frontend Debugging

```typescript
// Browser DevTools
console.log('Debug info:', data);
console.table(arrayData);
debugger; // Breakpoint

// React DevTools
// Install React Developer Tools browser extension
```

### API Testing

```bash
# Test endpoints with curl
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello"}]}'

# Use httpie (more user-friendly)
pip install httpie
http POST localhost:8000/chat messages:='[{"role": "user", "content": "Hello"}]'
```

## 📊 Performance Optimization

### Backend Performance

```python
# Use async/await for I/O operations
async def fetch_data():
    async with httpx.AsyncClient() as client:
        response = await client.get("https://api.example.com")
        return response.json()

# Database query optimization
def get_memories_optimized(limit: int = 100):
    # Use proper indexing and pagination
    return collection.query(
        n_results=limit,
        include=["documents", "metadatas"]
    )
```

### Frontend Performance

```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* Complex rendering */}</div>;
});

// Use useMemo for expensive calculations
const processedData = useMemo(() => {
  return data.map(item => expensiveTransform(item));
}, [data]);

// Use useCallback for event handlers
const handleClick = useCallback(() => {
  onAction(id);
}, [onAction, id]);
```

## 🔒 Security Guidelines

### Input Validation

```python
# Backend validation with Pydantic
from pydantic import BaseModel, validator

class MessageRequest(BaseModel):
    content: str
    
    @validator('content')
    def validate_content(cls, v):
        if len(v.strip()) == 0:
            raise ValueError('Content cannot be empty')
        if len(v) > 10000:
            raise ValueError('Content too long')
        return v.strip()
```

```typescript
// Frontend validation
function validateInput(input: string): string | null {
  if (!input.trim()) return 'Input cannot be empty';
  if (input.length > 1000) return 'Input too long';
  return null;
}
```

### API Security

```python
# Rate limiting (future implementation)
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/chat")
@limiter.limit("10/minute")
async def chat_endpoint(request: Request):
    pass
```

## 📚 Resources

### Documentation
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [ChromaDB Documentation](https://docs.trychroma.com/)

### Learning Resources
- [Python Type Hints](https://docs.python.org/3/library/typing.html)
- [React Hooks](https://react.dev/reference/react)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Modern JavaScript](https://javascript.info/)

### Tools
- [Postman](https://www.postman.com/) - API testing
- [React DevTools](https://react.dev/learn/react-developer-tools) - React debugging
- [Python Debugger](https://docs.python.org/3/library/pdb.html) - Python debugging

---

*This development guide is a living document. Please update it as the project evolves and new patterns emerge.*
