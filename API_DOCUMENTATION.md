# MindChat API Documentation

## 📋 Overview

MindChat provides a RESTful API built with FastAPI for managing conversations, memories, and AI interactions. All endpoints return JSON responses and follow standard HTTP status codes.

**Base URL**: `http://localhost:8000`
**API Documentation**: `http://localhost:8000/docs` (Swagger UI)

## 🔐 Authentication

Currently, the API operates without authentication. Future versions will implement JWT-based authentication.

## 📡 API Endpoints

### Health Check

#### GET `/`
Check if the backend service is running.

**Response**:
```json
{
  "message": "MindChat Backend is running"
}
```

---

### Chat API

#### POST `/chat`
Process a chat message and return AI response with memory context.

**Request Body**:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?",
      "timestamp": "2024-01-01T12:00:00Z",
      "web_search_enabled": false
    }
  ]
}
```

**Response**:
```json
{
  "assistant_message": {
    "role": "assistant",
    "content": "Hello! I'm doing well, thank you for asking...",
    "timestamp": "2024-01-01T12:00:01Z"
  },
  "retrieved_memory": [
    {
      "id": "uuid-string",
      "content": "Previous conversation context...",
      "distance": 0.234,
      "metadata": {
        "role": "user",
        "topic": "greetings",
        "timestamp": "2024-01-01T11:00:00Z"
      }
    }
  ],
  "raw_search_results": []
}
```

**Features**:
- Automatic memory search for context
- Web search integration (when enabled)
- Chart.js and Mermaid diagram generation
- Message persistence in memory

---

### Memory API

#### GET `/api/memory`
Retrieve stored memory entries with pagination and filtering.

**Query Parameters**:
- `skip` (int, default: 0): Number of records to skip
- `limit` (int, default: 100, max: 1000): Number of records to return
- `topic` (string, optional): Filter by topic

**Response**:
```json
[
  {
    "id": "uuid-string",
    "content": "Message content...",
    "role": "user",
    "timestamp": "2024-01-01T12:00:00Z",
    "metadata": {
      "topic": "general",
      "role": "user"
    }
  }
]
```

#### PUT `/api/memory/{memory_id}`
Update the content of a specific memory entry.

**Request Body**:
```json
{
  "content": "Updated message content...",
  "topic": "updated_topic"
}
```

**Response**:
```json
{
  "message": "Memory updated successfully"
}
```

#### DELETE `/api/memory/{memory_id}`
Delete a specific memory entry.

**Response**:
```json
{
  "message": "Memory deleted successfully"
}
```

#### POST `/api/memory/search`
Search for memories using semantic similarity.

**Request Body**:
```json
{
  "query": "search query text",
  "limit": 10,
  "topic": "optional_topic_filter"
}
```

**Response**:
```json
[
  {
    "id": "uuid-string",
    "content": "Matching memory content...",
    "distance": 0.123,
    "metadata": {
      "role": "assistant",
      "topic": "search_topic",
      "timestamp": "2024-01-01T12:00:00Z"
    }
  }
]
```

#### GET `/api/memory/topics`
Get a list of all unique topics in the memory database.

**Response**:
```json
[
  "general",
  "technical",
  "personal",
  "work"
]
```

---

## 📊 Data Models

### Message
```typescript
interface Message {
  role: "user" | "assistant";
  content: string;
  timestamp?: string;
  web_search_enabled?: boolean;
  id?: string;
}
```

### StoredMessage
```typescript
interface StoredMessage {
  id: string;
  content: string;
  role: string;
  timestamp?: string;
  metadata: {
    [key: string]: any;
    topic?: string;
    role: string;
  };
}
```

### RetrievedMemory
```typescript
interface RetrievedMemory {
  id: string;
  content: string;
  distance: number;
  metadata: {
    [key: string]: any;
    topic?: string;
    role: string;
    timestamp?: string;
  };
}
```

### ChatResponse
```typescript
interface ChatResponse {
  assistant_message: Message;
  retrieved_memory: RetrievedMemory[];
  raw_search_results: SearchResult[];
}
```

### SearchResult
```typescript
interface SearchResult {
  title?: string;
  link?: string;
  snippet?: string;
}
```

---

## 🔧 Configuration

### Environment Variables

#### Required for AI Integration
```bash
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE_URL=https://api.openai.com/v1  # Optional, defaults to OpenAI
LLM_MODEL_NAME=gpt-3.5-turbo  # Optional, defaults to gpt-3.5-turbo
```

#### Optional for Web Search
```bash
SERPAPI_API_KEY=your_serpapi_key_here
BRAVE_API_KEY=your_brave_api_key_here
```

#### Optional for Performance
```bash
HF_HUB_DISABLE_SYMLINKS_WARNING=1  # Disable Hugging Face warnings
```

---

## 🚨 Error Handling

### HTTP Status Codes
- `200 OK`: Successful request
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error

### Error Response Format
```json
{
  "detail": "Error description message"
}
```

### Common Errors

#### Chat API Errors
- `400`: No messages provided
- `400`: Latest message must be from user
- `500`: LLM configuration error
- `500`: Error calling LLM provider

#### Memory API Errors
- `404`: Memory not found
- `500`: Error retrieving memories
- `500`: Error updating memory
- `500`: Error deleting memory

---

## 🔍 Advanced Features

### Chart Generation
The chat API can generate Chart.js configurations when users request visualizations. Charts are embedded in the response content using markdown code blocks:

```markdown
```chartjs
{
  "type": "bar",
  "data": {
    "labels": ["Q1", "Q2", "Q3", "Q4"],
    "datasets": [{
      "label": "Sales",
      "data": [12, 19, 3, 5]
    }]
  }
}
```
```

### Mermaid Diagrams
Similarly, Mermaid diagrams are generated for flowcharts and process diagrams:

```markdown
```mermaid
flowchart TD
    A[Start] --> B[Process]
    B --> C[End]
```
```

### Memory Context
The system automatically searches for relevant memories based on the user's message and includes them as context for the AI response. This enables conversation continuity and reference to previous discussions.

### Web Search Integration
When enabled, the system can perform web searches to provide up-to-date information. Search results are included in the AI's context and returned in the response.

---

## 📈 Rate Limits

Currently, no rate limits are implemented. Future versions will include:
- 100 requests per minute per IP
- 1000 requests per hour per IP
- Configurable limits for different endpoints

---

## 🔮 Future API Enhancements

### Planned Endpoints
- `GET /api/conversations`: List conversation threads
- `POST /api/conversations`: Create new conversation
- `GET /api/settings`: User settings management
- `POST /api/export`: Export conversation data
- `POST /api/import`: Import conversation data

### Planned Features
- User authentication and authorization
- Conversation threading
- Advanced search filters
- Real-time updates via WebSocket
- File upload and processing

---

*For the most up-to-date API documentation, visit the interactive Swagger UI at `http://localhost:8000/docs` when the backend is running.*
