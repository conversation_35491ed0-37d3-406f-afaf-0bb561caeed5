# mindchat/backend/shared/config.py
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
# Look for .env file in the project root (parent of backend directory)
env_path = Path(__file__).parent.parent.parent / ".env"
load_dotenv(dotenv_path=env_path)

# Define environment variable names for configuration
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
OPENAI_API_BASE_URL = os.environ.get("OPENAI_API_BASE_URL", "https://api.openai.com/v1") # Default to OpenAI's base URL
LLM_MODEL_NAME = os.environ.get("LLM_MODEL_NAME", "gpt-3.5-turbo") # Default model

# --- New Web Search Configuration ---
SERPAPI_API_KEY = os.environ.get("SERPAPI_API_KEY")
BRAVE_API_KEY = os.environ.get("BRAVE_API_KEY")

# You can add other configuration variables here later
# e.g., CHROMA_DB_PATH = os.environ.get("CHROMA_DB_PATH", "./chroma_db")
