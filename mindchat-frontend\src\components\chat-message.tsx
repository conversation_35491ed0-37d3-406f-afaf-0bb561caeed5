﻿import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { Info, ExternalLink, Download } from 'lucide-react';
import mermaid from 'mermaid';
import * as domtoimage from 'dom-to-image';
import { ChartRenderer } from './client-chart';

// Define interfaces
interface RetrievedMemory {
    id: string;
    content: string;
    distance: number;
    metadata: { [key: string]: any };
}

interface SearchResult {
    title?: string;
    link?: string;
    snippet?: string;
}

interface ChatMessageProps {
    message: {
        role: 'user' | 'assistant';
        content: string;
        timestamp?: string;
        web_search_enabled?: boolean;
        retrievedMemory?: RetrievedMemory[];
        searchResults?: SearchResult[];
        id?: string;
    };
}

// Enhanced Mermaid syntax cleaner with better arrow label handling
function cleanMermaidSyntax(syntax: string): string {
    const lines = syntax
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

    const cleanedLines = lines.map(line => {
        if (line.match(/^(flowchart|graph|sequenceDiagram|classDiagram)/)) {
            return line;
        }

        return line
            .replace(/-->\s*"([^"]*)"/, ' --> ')
            .replace(/==>\s*"([^"]*)"/, ' ==> ')
            .replace(/-..->\s*"([^"]*)"/, ' -.-> ')
            .replace(/-->\s*\|([^|]*)\|/, ' --> ')
            .replace(/==>\s*\|([^|]*)\|/, ' ==> ')
            .replace(/-..->\s*\|([^|]*)\|/, ' -.-> ')
            .replace(/\[([^\]]*[&<>|"'].*?)\]/g, (match, content) => {
                const cleaned = content
                    .replace(/[&<>|"']/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                return `[${cleaned}]`;
            })
            .replace(/\(([^)]*[&<>|"'].*?)\)/g, (match, content) => {
                const cleaned = content
                    .replace(/[&<>|"']/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                return `(${cleaned})`;
            })
            .replace(/\{([^}]*[&<>|"'].*?)\}/g, (match, content) => {
                const cleaned = content
                    .replace(/[&<>|"']/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                return `{${cleaned}}`;
            })
            .replace(/\s*-->\s*/, ' --> ')
            .replace(/\s*==>\s*/, ' ==> ')
            .replace(/\s*-\.->\s*/, ' -.-> ');
    });

    return cleanedLines.join('\n');
}

// JSON repair utility function
function repairJson(jsonString: string): string {
    try {
        JSON.parse(jsonString);
        return jsonString;
    } catch (e) {
        let repaired = jsonString
            .replace(/'/g, '"')
            .replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":')
            .replace(/,(\s*[}\]])/g, '$1')
            .replace(/\/\/.*$/gm, '')
            .replace(/\/\*[\s\S]*?\*\//g, '')
            .replace(/\\\n/g, '\\n')
            .replace(/\\\t/g, '\\t')
            .replace(/\\\r/g, '\\r');

        try {
            JSON.parse(repaired);
            console.log('JSON successfully repaired');
            return repaired;
        } catch (e2) {
            console.error('Could not repair JSON:', e2);
            throw new Error(`Invalid JSON that cannot be repaired: ${e2.message}`);
        }
    }
}

// Fallback function for simple Mermaid
function createFallbackMermaid(): string {
    return `flowchart TD
    A[Start]
    B[Process]
    C[Decision]
    D[End]
    A --> B
    B --> C
    C --> D`;
}

// Initialize mermaid with enhanced settings
const MERMAID_BASE_ID = 'mermaid-svg-';
mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    flowchart: {
        htmlLabels: false,
        useMaxWidth: false,
        curve: 'basis'
    },
    sequence: {
        useMaxWidth: false,
    },
    gantt: {
        useMaxWidth: false,
    }
});

export function ChatMessage({ message }: ChatMessageProps) {
    const [showMemory, setShowMemory] = useState(false);
    const [showSources, setShowSources] = useState(false);
    const mermaidRef = useRef<HTMLDivElement>(null);
    const [mermaidSyntax, setMermaidSyntax] = useState<string | null>(null);
    const [chartConfig, setChartConfig] = useState<any | null>(null);
    const [markdownContent, setMarkdownContent] = useState<string>('');
    const [diagramLoading, setDiagramLoading] = useState(false);
    const [renderError, setRenderError] = useState<string | null>(null);
    const [renderedSvg, setRenderedSvg] = useState<string | null>(null);
    const [diagramZoom, setDiagramZoom] = useState(1);
    const [isFullscreen, setIsFullscreen] = useState(false);

    const memoryUsed = message.role === 'assistant' && message.retrievedMemory && message.retrievedMemory.length > 0;
    const searchResultsAvailable = message.role === 'assistant' && message.searchResults && message.searchResults.length > 0;

    const toggleMemoryVisibility = () => setShowMemory(!showMemory);
    const toggleSourcesVisibility = () => setShowSources(!showSources);

    // Enhanced zoom handler
    const handleZoomDiagram = (action: 'in' | 'out' | 'reset') => {
        if (!mermaidRef.current) return;

        const container = mermaidRef.current.querySelector('.mermaid-container > div') as HTMLElement;
        if (!container) return;

        let newZoom = diagramZoom;

        switch (action) {
            case 'in':
                newZoom = Math.min(diagramZoom * 1.2, 3);
                break;
            case 'out':
                newZoom = Math.max(diagramZoom * 0.8, 0.3);
                break;
            case 'reset':
                newZoom = 1;
                break;
        }

        setDiagramZoom(newZoom);
        container.style.transform = `scale(${newZoom})`;
        container.style.transformOrigin = 'center';
    };

    // Enhanced fullscreen handler
    const handleFullScreenDiagram = () => {
        if (!mermaidRef.current) return;

        const diagramContainer = mermaidRef.current;

        if (!isFullscreen) {
            const fullscreenModal = document.createElement('div');
            fullscreenModal.id = 'mermaid-fullscreen-modal';
            fullscreenModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const diagramClone = diagramContainer.cloneNode(true) as HTMLElement;
            diagramClone.style.cssText = `
                max-width: 90vw;
                max-height: 90vh;
                background: white;
                border-radius: 8px;
                overflow: auto;
            `;

            const closeButton = document.createElement('button');
            closeButton.innerHTML = '✕ Close';
            closeButton.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                background: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                z-index: 10001;
                font-size: 14px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            `;

            closeButton.onclick = () => {
                document.body.removeChild(fullscreenModal);
                setIsFullscreen(false);
            };

            fullscreenModal.onclick = (e) => {
                if (e.target === fullscreenModal) {
                    document.body.removeChild(fullscreenModal);
                    setIsFullscreen(false);
                }
            };

            const handleEsc = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(fullscreenModal);
                    setIsFullscreen(false);
                    document.removeEventListener('keydown', handleEsc);
                }
            };
            document.addEventListener('keydown', handleEsc);

            fullscreenModal.appendChild(diagramClone);
            fullscreenModal.appendChild(closeButton);
            document.body.appendChild(fullscreenModal);
            setIsFullscreen(true);
        }
    };

    // Extract content with enhanced cleaning
    useEffect(() => {
        if (message.role === 'assistant' && message.content) {
            let content = message.content;

            // Extract Mermaid syntax with aggressive cleaning
            const mermaidMatch = /```mermaid\n([\s\S]*?)\n```/.exec(content);
            if (mermaidMatch && mermaidMatch[1]) {
                const extractedSyntax = mermaidMatch[1].trim();
                const cleanedSyntax = cleanMermaidSyntax(extractedSyntax);

                console.log('Original Mermaid:', extractedSyntax);
                console.log('Cleaned Mermaid:', cleanedSyntax);

                const lines = cleanedSyntax.split('\n').filter(l => l.trim().length > 0);
                if (lines.length > 0 && lines[0].match(/^(flowchart|graph)/)) {
                    const hasProblematicSyntax = cleanedSyntax.includes('"') ||
                        cleanedSyntax.includes('|') ||
                        cleanedSyntax.match(/-->\s*[A-Z]/);

                    if (hasProblematicSyntax) {
                        console.warn('Still has problematic syntax after cleaning');
                        const fallbackSyntax = createFallbackMermaid();
                        setMermaidSyntax(fallbackSyntax);
                    } else {
                        setMermaidSyntax(cleanedSyntax);
                    }
                } else {
                    console.warn('Invalid Mermaid diagram structure');
                    setRenderError('Invalid Mermaid diagram structure');
                    setMermaidSyntax(null);
                }

                content = content.replace(mermaidMatch[0], '').trim();
            } else {
                setMermaidSyntax(null);
            }

            // Extract Chart.js config with improved parsing
            const chartMatch = /```chartjs\n([\s\S]*?)\n```/.exec(content);
            if (chartMatch && chartMatch[1]) {
                try {
                    const chartJsonString = chartMatch[1].trim();
                    const config = JSON.parse(repairJson(chartJsonString));

                    if (config && typeof config === 'object' && config.type && config.data) {
                        setChartConfig(config);
                        content = content.replace(chartMatch[0], '').trim();
                        console.log('Chart.js config successfully parsed:', config);
                    } else {
                        console.error('Invalid chart structure');
                        setChartConfig(null);
                    }
                } catch (e) {
                    console.error('Failed to parse Chart.js config:', e);
                    setChartConfig(null);
                    content = content.replace(chartMatch[0], `\`\`\`json\n${chartMatch[1]}\n\`\`\``);
                }
            } else {
                setChartConfig(null);
            }

            setMarkdownContent(content);
        } else {
            setMermaidSyntax(null);
            setChartConfig(null);
            setMarkdownContent(message.content || '');
        }
        setRenderedSvg(null);
        setRenderError(null);
        setDiagramZoom(1);
    }, [message.content, message.role]);

    // Render Mermaid with improved error handling
    useEffect(() => {
        if (!mermaidSyntax || renderedSvg) return;

        setDiagramLoading(true);
        setRenderError(null);

        const uniqueId = message.id || `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const renderId = `${MERMAID_BASE_ID}${uniqueId.replace(/[^a-zA-Z0-9_-]/g, '_')}`;

        const tempRenderDiv = document.createElement('div');
        tempRenderDiv.id = renderId;
        tempRenderDiv.style.position = 'absolute';
        tempRenderDiv.style.left = '-9999px';
        document.body.appendChild(tempRenderDiv);

        const isValidDiagramType = mermaidSyntax.match(/^(flowchart|graph|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gantt|pie)/);
        if (!isValidDiagramType) {
            console.warn("Invalid Mermaid diagram type:", mermaidSyntax);
            setRenderError('Invalid Mermaid diagram syntax. Displaying raw code.');
            setDiagramLoading(false);
            tempRenderDiv.parentNode?.removeChild(tempRenderDiv);
            return;
        }

        try {
            console.log('Attempting to render Mermaid:', mermaidSyntax);
            mermaid.render(renderId, mermaidSyntax)
                .then((result) => {
                    setRenderedSvg(result.svg);
                    tempRenderDiv.parentNode?.removeChild(tempRenderDiv);
                    setDiagramLoading(false);
                })
                .catch((error) => {
                    console.error(`Error rendering mermaid:`, error);
                    setRenderError('Failed to render diagram. Displaying raw code.');
                    setRenderedSvg(null);
                    tempRenderDiv.parentNode?.removeChild(tempRenderDiv);
                    setDiagramLoading(false);
                });
        } catch (error) {
            console.error("Error initializing mermaid render:", error);
            setRenderError('Failed to render diagram. Displaying raw code.');
            setRenderedSvg(null);
            tempRenderDiv.parentNode?.removeChild(tempRenderDiv);
            setDiagramLoading(false);
        }

        return () => {
            const tempDiv = document.getElementById(renderId);
            if (tempDiv && tempDiv.parentNode) {
                tempDiv.parentNode.removeChild(tempDiv);
            }
        };
    }, [mermaidSyntax, message.id, renderedSvg]);

    // Enhanced download function
    const handleDownloadPng = async () => {
        if (mermaidRef.current && renderedSvg) {
            setDiagramLoading(true);
            try {
                const diagramElement = mermaidRef.current.querySelector('.mermaid-container') as HTMLElement;
                if (!diagramElement) {
                    throw new Error('Diagram element not found');
                }

                const dataUrl = await domtoimage.toPng(diagramElement, {
                    quality: 1.0,
                    bgcolor: '#ffffff',
                    width: diagramElement.scrollWidth * diagramZoom,
                    height: diagramElement.scrollHeight * diagramZoom,
                });

                const link = document.createElement('a');
                link.download = `mermaid-diagram-${message.id || Date.now()}.png`;
                link.href = dataUrl;
                link.click();
            } catch (error) {
                console.error("Error converting diagram to PNG:", error);
                alert("Failed to download diagram. Please try again.");
            } finally {
                setDiagramLoading(false);
            }
        }
    };

    return (
        <div className={`flex w-full ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex flex-col max-w-6xl rounded-lg ${message.role === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-black'
                }`}>
                <div className="flex items-start px-4 py-2">
                    <div className="flex-grow">
                        {markdownContent && (
                            <ReactMarkdown
                                remarkPlugins={[remarkGfm]}
                                components={{
                                    code({ inline, className, children, ...props }) {
                                        const match = /language-(\w+)/.exec(className || '');
                                        if (match && (match[1] === 'mermaid' || match[1] === 'chartjs')) return null;
                                        return !inline && match ? (
                                            <SyntaxHighlighter style={dracula} language={match[1]} PreTag="div" {...props}>
                                                {String(children).replace(/\n$/, '')}
                                            </SyntaxHighlighter>
                                        ) : (
                                            <code className={className} {...props}>{children}</code>
                                        );
                                    },
                                }}
                            >
                                {markdownContent}
                            </ReactMarkdown>
                        )}

                        {chartConfig && (
                            <ChartRenderer
                                config={chartConfig}
                                messageId={message.id}
                            />
                        )}

                        {mermaidSyntax && (
                            <div className="my-4">
                                <div
                                    ref={mermaidRef}
                                    className="overflow-auto bg-white border rounded-lg shadow-sm"
                                    style={{
                                        maxHeight: '80vh',
                                        maxWidth: '100%',
                                        minHeight: '200px'
                                    }}
                                >
                                    {diagramLoading && !renderedSvg && (
                                        <div className="flex items-center justify-center h-32">
                                            <p className="text-gray-500">Rendering diagram...</p>
                                        </div>
                                    )}

                                    {renderError && (
                                        <div className="p-4">
                                            <div className="text-red-600 text-sm mb-2">{renderError}</div>
                                            <SyntaxHighlighter style={dracula} language="mermaid" PreTag="div">
                                                {mermaidSyntax}
                                            </SyntaxHighlighter>
                                        </div>
                                    )}

                                    {renderedSvg && (
                                        <div className="mermaid-container p-4 flex justify-center items-center min-h-[200px]">
                                            <div
                                                dangerouslySetInnerHTML={{ __html: renderedSvg }}
                                                style={{
                                                    transform: `scale(${diagramZoom})`,
                                                    transformOrigin: 'center',
                                                    maxWidth: 'none'
                                                }}
                                            />
                                        </div>
                                    )}
                                </div>

                                {renderedSvg && !diagramLoading && !renderError && (
                                    <div className="flex justify-center items-center gap-2 mt-3 p-2 bg-gray-50 rounded flex-wrap">
                                        <button
                                            onClick={() => handleZoomDiagram('in')}
                                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                                        >
                                            🔍+ Zoom In
                                        </button>

                                        <button
                                            onClick={() => handleZoomDiagram('out')}
                                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                                        >
                                            🔍- Zoom Out
                                        </button>

                                        <button
                                            onClick={() => handleZoomDiagram('reset')}
                                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                                        >
                                            ↻ Reset
                                        </button>

                                        <button
                                            onClick={handleFullScreenDiagram}
                                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                                        >
                                            ⛶ Fullscreen
                                        </button>

                                        <button
                                            onClick={handleDownloadPng}
                                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                                            disabled={diagramLoading}
                                        >
                                            <Download size={14} className="mr-1" /> Download
                                        </button>

                                        <span className="text-xs text-gray-500 px-2">
                                            Zoom: {Math.round(diagramZoom * 100)}%
                                        </span>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="flex flex-col ml-2 space-y-1">
                        {memoryUsed && (
                            <button
                                onClick={toggleMemoryVisibility}
                                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                                title={showMemory ? "Hide Memory" : "Show Memory Used"}
                            >
                                <Info size={16} />
                            </button>
                        )}
                        {searchResultsAvailable && (
                            <button
                                onClick={toggleSourcesVisibility}
                                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                                title={showSources ? "Hide Sources" : "Show Sources Used"}
                            >
                                <ExternalLink size={16} />
                            </button>
                        )}
                    </div>
                </div>

                {memoryUsed && showMemory && (
                    <div className="border-t border-gray-300 p-4 text-sm text-gray-700 mt-2">
                        <h4 className="font-semibold mb-2">Memory Used:</h4>
                        <ul className="list-disc list-inside space-y-2">
                            {message.retrievedMemory?.map((memory, index) => (
                                <li key={index}>
                                    <p className="italic">{memory.content}</p>
                                    <p className="text-xs text-gray-500">
                                        Distance: {memory.distance.toFixed(3)} |
                                        Topic: {memory.metadata?.topic || 'N/A'}
                                    </p>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}

                {searchResultsAvailable && showSources && (
                    <div className="border-t border-gray-300 p-4 text-sm text-gray-700 mt-2">
                        <h4 className="font-semibold mb-2">Sources:</h4>
                        <ul className="list-disc list-inside space-y-2">
                            {message.searchResults?.map((source, index) => (
                                <li key={index}>
                                    {source.title && (
                                        <a href={source.link} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                            {source.title}
                                        </a>
                                    )}
                                    {source.link && !source.title && (
                                        <a href={source.link} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                            {source.link}
                                        </a>
                                    )}
                                    {source.snippet && (
                                        <p className="text-xs text-gray-600 mt-1">{source.snippet}</p>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
}
