"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Pencil, Trash2, Save, XCircle } from 'lucide-react';

interface StoredMessage {
    id: string;
    content: string;
    role: string;
    timestamp?: string;
    metadata: { [key: string]: any };
}

export function MemoryBrowser() {
    const [memories, setMemories] = useState<StoredMessage[]>([]);
    const [loading, setLoading] = useState(true);
    const [editingId, setEditingId] = useState<string | null>(null);
    const [editContent, setEditContent] = useState<string>('');
    const [page, setPage] = useState(0); // For pagination
    const [hasMore, setHasMore] = useState(true); // For pagination
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedTopic, setSelectedTopic] = useState<string>('all');
    const [topics, setTopics] = useState<string[]>([]);
    const [filteredMemories, setFilteredMemories] = useState<StoredMessage[]>([]);

    const fetchMemories = async (pageToFetch = 0) => {
        setLoading(true);
        try {
            const limit = 50; // Number of memories per page
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/api/memory?skip=${pageToFetch * limit}&limit=${limit}`);
            if (!response.ok) {
                throw new Error(`Error fetching memories: ${response.statusText}`);
            }
            const data: StoredMessage[] = await response.json();
            setMemories(prev => pageToFetch === 0 ? data : [...prev, ...data]);
            setHasMore(data.length === limit); // Check if there might be more pages
            setPage(pageToFetch);

            // Extract unique topics
            const allTopics = data.reduce((acc: string[], memory) => {
                const topic = memory.metadata?.topic;
                if (topic && !acc.includes(topic)) {
                    acc.push(topic);
                }
                return acc;
            }, []);
            setTopics(prev => [...new Set([...prev, ...allTopics])]);
        } catch (error) {
            console.error('Failed to fetch memories:', error);
            // Show user-friendly error message
            alert('Failed to load memories. Please check if the backend is running and try again.');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchMemories();
    }, []); // Fetch on component mount

    // Filter memories based on search query and selected topic
    useEffect(() => {
        let filtered = memories;

        // Filter by topic
        if (selectedTopic !== 'all') {
            filtered = filtered.filter(memory => memory.metadata?.topic === selectedTopic);
        }

        // Filter by search query
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(memory =>
                memory.content.toLowerCase().includes(query) ||
                memory.role.toLowerCase().includes(query) ||
                (memory.metadata?.topic && memory.metadata.topic.toLowerCase().includes(query))
            );
        }

        setFilteredMemories(filtered);
    }, [memories, searchQuery, selectedTopic]);

    const handleEditClick = (memory: StoredMessage) => {
        setEditingId(memory.id);
        setEditContent(memory.content);
    };

    const handleCancelEdit = () => {
        setEditingId(null);
        setEditContent('');
    };

    const handleSaveEdit = async (id: string) => {
        setLoading(true); // Show loading while saving
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/api/memory/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ content: editContent }),
            });
            if (!response.ok) {
                throw new Error(`Error updating memory: ${response.statusText}`);
            }
            // Update the memory in the local state
            setMemories(memories.map(mem =>
                mem.id === id ? { ...mem, content: editContent } : mem
            ));
            setEditingId(null);
            setEditContent('');
        } catch (error) {
            console.error(`Failed to save memory ${id}:`, error);
            alert('Failed to save memory. Please try again.'); // User feedback
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteClick = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this memory?')) {
            setLoading(true); // Show loading while deleting
            try {
                const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
                const response = await fetch(`${apiUrl}/api/memory/${id}`, {
                    method: 'DELETE',
                });
                if (!response.ok) {
                    throw new Error(`Error deleting memory: ${response.statusText}`);
                }
                // Remove the memory from the local state
                setMemories(memories.filter(mem => mem.id !== id));
            } catch (error) {
                console.error(`Failed to delete memory ${id}:`, error);
                alert('Failed to delete memory. Please try again.'); // User feedback
            } finally {
                setLoading(false);
            }
        }
    };

    const handleLoadMore = () => {
        fetchMemories(page + 1);
    };

    return (
        <div className="space-y-4">
            {/* Search and Filter Controls */}
            <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    <div className="flex-1">
                        <label className="block text-sm font-medium mb-1">Search Memories</label>
                        <input
                            type="text"
                            placeholder="Search by content, role, or topic..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>
                    <div className="md:w-48">
                        <label className="block text-sm font-medium mb-1">Filter by Topic</label>
                        <select
                            value={selectedTopic}
                            onChange={(e) => setSelectedTopic(e.target.value)}
                            className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="all">All Topics</option>
                            {topics.map(topic => (
                                <option key={topic} value={topic}>{topic}</option>
                            ))}
                        </select>
                    </div>
                </div>
                <div className="text-sm text-gray-600">
                    Showing {filteredMemories.length} of {memories.length} memories
                    {searchQuery && ` matching "${searchQuery}"`}
                    {selectedTopic !== 'all' && ` in topic "${selectedTopic}"`}
                </div>
            </div>

            {loading && page === 0 && <p>Loading memories...</p>} {/* Initial loading */}
            {filteredMemories.map(memory => (
                <div key={memory.id} className="border rounded-lg p-4 shadow-sm bg-white">
                    {editingId === memory.id ? (
                        <div className="flex flex-col space-y-2">
                            <Textarea
                                value={editContent}
                                onChange={(e) => setEditContent(e.target.value)}
                                rows={4}
                            />
                            <div className="flex space-x-2 justify-end">
                                <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                                    <XCircle size={16} className="mr-1" /> Cancel
                                </Button>
                                <Button size="sm" onClick={() => handleSaveEdit(memory.id)} disabled={loading}>
                                    <Save size={16} className="mr-1" /> Save
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="text-sm text-gray-500 mb-1">
                                {memory.role} - {memory.timestamp ? new Date(memory.timestamp).toLocaleString() : 'No Timestamp'}
                            </div>
                            {memory.metadata?.topic && (
                                <div className="text-xs text-gray-600 mb-2">
                                    Topic: {memory.metadata.topic}
                                </div>
                            )}
                            <p className="mb-3">{memory.content}</p>
                            <div className="flex space-x-2 justify-end">
                                <Button variant="outline" size="sm" onClick={() => handleEditClick(memory)} disabled={loading}>
                                    <Pencil size={16} className="mr-1" /> Edit
                                </Button>
                                <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(memory.id)} disabled={loading}>
                                    <Trash2 size={16} className="mr-1" /> Delete
                                </Button>
                            </div>
                        </>
                    )}
                </div>
            ))}

            {loading && page > 0 && <p>Loading more memories...</p>} {/* Loading subsequent pages */}
            {!loading && hasMore && (
                <div className="flex justify-center">
                    <Button onClick={handleLoadMore}>Load More</Button>
                </div>
            )}
            {!loading && filteredMemories.length === 0 && memories.length > 0 && (
                <p className="text-center text-gray-500">No memories match your search criteria.</p>
            )}
            {!loading && memories.length === 0 && <p className="text-center text-gray-500">No memories found.</p>}
        </div>
    );
}