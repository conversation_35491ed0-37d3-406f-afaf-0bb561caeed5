"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BookOpen, 
  MessageSquare, 
  Brain, 
  BarChart3, 
  Settings, 
  Search,
  Download,
  Upload,
  Zap,
  Shield,
  Globe,
  Code,
  ExternalLink
} from 'lucide-react';

export default function DocsPage() {
  const features = [
    {
      icon: <MessageSquare className="h-6 w-6" />,
      title: "AI Chat Interface",
      description: "Real-time conversations with AI, supporting multiple providers and mock mode for testing.",
      status: "Available"
    },
    {
      icon: <Brain className="h-6 w-6" />,
      title: "Memory Management",
      description: "Advanced memory system with semantic search, editing, and deletion capabilities.",
      status: "Available"
    },
    {
      icon: <BarChart3 className="h-6 w-6" />,
      title: "Data Visualization",
      description: "Dynamic Chart.js charts and Mermaid diagrams generated from AI responses.",
      status: "Available"
    },
    {
      icon: <Search className="h-6 w-6" />,
      title: "Web Search Integration",
      description: "AI can search the web for current information when enabled.",
      status: "Available"
    },
    {
      icon: <Settings className="h-6 w-6" />,
      title: "Settings Management",
      description: "Comprehensive settings for API keys, preferences, and advanced options.",
      status: "New"
    }
  ];

  const quickStart = [
    {
      step: 1,
      title: "Start Chatting",
      description: "Navigate to the main chat interface and start a conversation with the AI."
    },
    {
      step: 2,
      title: "Explore Memory",
      description: "Visit the Memory page to view, edit, and manage your conversation history."
    },
    {
      step: 3,
      title: "Configure Settings",
      description: "Set up your API keys and preferences in the Settings page."
    },
    {
      step: 4,
      title: "Generate Visualizations",
      description: "Ask the AI to create charts or diagrams to see the visualization features."
    }
  ];

  const apiEndpoints = [
    {
      method: "POST",
      endpoint: "/chat",
      description: "Send messages to AI and receive responses with memory context"
    },
    {
      method: "GET",
      endpoint: "/api/memory",
      description: "Retrieve stored conversation memories with pagination"
    },
    {
      method: "PUT",
      endpoint: "/api/memory/{id}",
      description: "Update the content of a specific memory entry"
    },
    {
      method: "DELETE",
      endpoint: "/api/memory/{id}",
      description: "Delete a specific memory entry from the database"
    },
    {
      method: "POST",
      endpoint: "/api/memory/search",
      description: "Perform semantic search through conversation memories"
    }
  ];

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4 flex items-center">
          <BookOpen className="mr-3 h-8 w-8" />
          MindChat Documentation
        </h1>
        <p className="text-xl text-gray-600">
          Comprehensive guide to using MindChat's AI-powered conversation and memory management system.
        </p>
      </div>

      {/* Quick Start Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <Zap className="mr-2 h-6 w-6" />
          Quick Start Guide
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickStart.map((item) => (
            <Card key={item.step} className="relative">
              <CardHeader>
                <div className="absolute -top-3 -left-3 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">
                  {item.step}
                </div>
                <CardTitle className="text-lg">{item.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Features Overview */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Features Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    {feature.icon}
                    <span className="ml-2">{feature.title}</span>
                  </div>
                  <Badge variant={feature.status === "New" ? "default" : "secondary"}>
                    {feature.status}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* API Reference */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <Code className="mr-2 h-6 w-6" />
          API Reference
        </h2>
        <Card>
          <CardHeader>
            <CardTitle>Available Endpoints</CardTitle>
            <CardDescription>
              RESTful API endpoints for integrating with MindChat programmatically.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {apiEndpoints.map((endpoint, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <Badge variant={endpoint.method === "GET" ? "secondary" : endpoint.method === "POST" ? "default" : "destructive"}>
                      {endpoint.method}
                    </Badge>
                    <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                      {endpoint.endpoint}
                    </code>
                  </div>
                  <p className="text-sm text-gray-600 max-w-md">{endpoint.description}</p>
                </div>
              ))}
            </div>
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Interactive API Documentation:</strong> Visit{' '}
                <a 
                  href="http://localhost:8000/docs" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="underline hover:text-blue-600"
                >
                  http://localhost:8000/docs
                </a>{' '}
                when the backend is running to explore the full API with examples.
              </p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Usage Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Usage Examples</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="mr-2 h-5 w-5" />
                Chat Examples
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Basic Conversation</h4>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <p><strong>You:</strong> "Hello! How are you today?"</p>
                  <p><strong>AI:</strong> "Hello! I'm doing well, thank you for asking..."</p>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Request a Chart</h4>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <p><strong>You:</strong> "Create a bar chart showing quarterly sales data"</p>
                  <p><strong>AI:</strong> "I'll create a sample chart for you!" <em>(generates Chart.js visualization)</em></p>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Request a Diagram</h4>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <p><strong>You:</strong> "Show me a flowchart of the user registration process"</p>
                  <p><strong>AI:</strong> "Here's a process diagram:" <em>(generates Mermaid flowchart)</em></p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="mr-2 h-5 w-5" />
                Memory Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">View Memories</h4>
                <p className="text-sm text-gray-600 mb-2">Navigate to the Memory page to see all stored conversations.</p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Edit Memory</h4>
                <p className="text-sm text-gray-600 mb-2">Click the edit button on any memory to modify its content.</p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Delete Memory</h4>
                <p className="text-sm text-gray-600 mb-2">Use the delete button to remove unwanted memories.</p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Search Memories</h4>
                <p className="text-sm text-gray-600 mb-2">Use semantic search to find relevant past conversations.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Configuration */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <Settings className="mr-2 h-6 w-6" />
          Configuration
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>API Keys Setup</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold">OpenAI API Key</h4>
                <p className="text-sm text-gray-600">Required for AI functionality. Get yours from OpenAI Platform.</p>
              </div>
              <div>
                <h4 className="font-semibold">Search API Keys (Optional)</h4>
                <p className="text-sm text-gray-600">SerpAPI or Brave Search for web search capabilities.</p>
              </div>
              <Button variant="outline" asChild>
                <a href="/settings">Configure in Settings</a>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Environment Variables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded text-sm font-mono space-y-1">
                <div>OPENAI_API_KEY=your_key_here</div>
                <div>LLM_MODEL_NAME=gpt-3.5-turbo</div>
                <div>SERPAPI_API_KEY=optional</div>
                <div>BRAVE_API_KEY=optional</div>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Set these in your backend environment or use the Settings page.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Troubleshooting */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <Shield className="mr-2 h-6 w-6" />
          Troubleshooting
        </h2>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold mb-2">AI Not Responding</h4>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Check if OpenAI API key is configured in Settings</li>
                  <li>• Verify backend is running on port 8000</li>
                  <li>• Check browser console for error messages</li>
                  <li>• Try using mock mode for testing</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Memory Not Loading</h4>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Ensure backend database is initialized</li>
                  <li>• Check network connection to backend</li>
                  <li>• Try refreshing the page</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Charts Not Displaying</h4>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Ensure JavaScript is enabled</li>
                  <li>• Check if Chart.js library loaded correctly</li>
                  <li>• Try requesting a simple chart first</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* External Resources */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <Globe className="mr-2 h-6 w-6" />
          External Resources
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6">
              <h4 className="font-semibold mb-2">OpenAI Platform</h4>
              <p className="text-sm text-gray-600 mb-3">Get your API key and manage usage</p>
              <Button variant="outline" size="sm" asChild>
                <a href="https://platform.openai.com" target="_blank" rel="noopener noreferrer">
                  Visit <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <h4 className="font-semibold mb-2">Chart.js Documentation</h4>
              <p className="text-sm text-gray-600 mb-3">Learn about chart types and options</p>
              <Button variant="outline" size="sm" asChild>
                <a href="https://www.chartjs.org/docs/" target="_blank" rel="noopener noreferrer">
                  Visit <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <h4 className="font-semibold mb-2">Mermaid Documentation</h4>
              <p className="text-sm text-gray-600 mb-3">Create diagrams and flowcharts</p>
              <Button variant="outline" size="sm" asChild>
                <a href="https://mermaid.js.org/" target="_blank" rel="noopener noreferrer">
                  Visit <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <div className="text-center py-8 border-t">
        <p className="text-gray-600">
          Need more help? Check the{' '}
          <Button variant="link" className="p-0 h-auto" asChild>
            <a href="http://localhost:8000/docs" target="_blank" rel="noopener noreferrer">
              API documentation
            </a>
          </Button>{' '}
          or visit the{' '}
          <Button variant="link" className="p-0 h-auto" asChild>
            <a href="/help">Help page</a>
          </Button>.
        </p>
      </div>
    </div>
  );
}
