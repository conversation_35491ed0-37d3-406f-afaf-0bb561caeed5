# backend/api/memory.py
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import shared schema
from shared.schema import StoredMessage, RetrievedMemory
from memory.embedding_generator import generate_embedding
from memory.chromadb_interface import (
    get_all_messages,
    update_message_content,
    delete_message_by_id,
    add_message_to_memory,
    search_memory,
    get_all_topics
)

router = APIRouter()

@router.get("/memory", response_model=List[StoredMessage])
async def get_memories(
    skip: int = Query(0, ge=0), 
    limit: int = Query(100, le=1000),
    topic: Optional[str] = None
):
    """
    Retrieve stored memory entries with pagination and optional topic filtering.
    """
    try:
        memories = get_all_messages(skip=skip, limit=limit, topic=topic)
        return [StoredMessage(**mem) for mem in memories]
    except Exception as e:
        print(f"Error retrieving memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving memories: {str(e)}")

class UpdateMemoryRequest(BaseModel):
    content: str
    topic: Optional[str] = None

class SearchMemoryRequest(BaseModel):
    query: str
    limit: int = 10
    topic: Optional[str] = None

@router.put("/memory/{memory_id}")
async def update_memory(memory_id: str, update_data: UpdateMemoryRequest):
    """
    Update the content of a specific memory entry.
    """
    try:
        success = update_message_content(memory_id, update_data.content, update_data.topic)
        if not success:
            raise HTTPException(status_code=404, detail="Memory not found")
        return {"message": "Memory updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error updating memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating memory: {str(e)}")

@router.delete("/memory/{memory_id}")
async def delete_memory(memory_id: str):
    """
    Delete a specific memory entry.
    """
    try:
        success = delete_message_by_id(memory_id)
        if not success:
            raise HTTPException(status_code=404, detail="Memory not found")
        return {"message": "Memory deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error deleting memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting memory: {str(e)}")

@router.post("/memory/search", response_model=List[RetrievedMemory])
async def search_memories(search_request: SearchMemoryRequest):
    """
    Search for memories using semantic similarity.
    """
    try:
        query_embedding = generate_embedding(search_request.query)
        results = search_memory(
            query_embedding, 
            n_results=search_request.limit,
            topic=search_request.topic
        )
        
        return [
            RetrievedMemory(
                id=item["id"],
                content=item["content"],
                distance=item["distance"],
                metadata=item["metadata"]
            ) for item in results
        ]
    except Exception as e:
        print(f"Error searching memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching memories: {str(e)}")

@router.get("/memory/topics", response_model=List[str])
async def get_topics():
    """
    Get a list of all unique topics in the memory database.
    """
    try:
        # This would require implementing get_all_topics in chromadb_interface
        topics = get_all_topics()
        return topics
    except Exception as e:
        print(f"Error retrieving topics: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving topics: {str(e)}")