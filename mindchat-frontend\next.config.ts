// next.config.ts
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    /* config options here */
    webpack: (config, { isServer }) => {
        // Only modify webpack config for server-side build
        if (isServer) {
            config.externals = {
                ...config.externals,
                // Exclude 'chartjs-plugin-zoom' from server-side bundle
                'chartjs-plugin-zoom': 'commonjs chartjs-plugin-zoom',
            };
        }
        return config;
    },
};

export default nextConfig;

