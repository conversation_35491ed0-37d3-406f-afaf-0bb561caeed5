# mindchat/backend/memory/chromadb_interface.py
import chromadb
from typing import List, Dict
from uuid import uuid4
from datetime import datetime, timezone

# Initialize ChromaDB client
# For a local-first application, a persistent client is suitable
# Replace with an in-memory client or different path if needed
client = chromadb.PersistentClient(path="./chroma_db")

# Get or create a collection
# A collection is like a table in a traditional database
collection_name = "mindchat_messages"
try:
    collection = client.create_collection(name=collection_name)
    print(f"Created ChromaDB collection: {collection_name}")
except:
    collection = client.get_collection(name=collection_name)
    print(f"Using existing ChromaDB collection: {collection_name}")

def add_message_to_memory(content: str, role: str, embedding: List[float], topic: str = None) -> str:
    """
    Adds a message and its embedding to the ChromaDB collection.
    Returns the ID of the added document.
    """
    doc_id = str(uuid4())
    metadata = {
        "role": role,
        "timestamp": datetime.now(timezone.utc).isoformat(), # Store timestamp as ISO format string
    }
    if topic:
        metadata["topic"] = topic

    collection.add(
        embeddings=[embedding],
        documents=[content],
        metadatas=[metadata],
        ids=[doc_id]
    )
    print(f"Added message {doc_id} to memory.")
    return doc_id

def search_memory(query_embedding: List[float], n_results: int = 5, topic: str = None) -> List[Dict]:
    """
    Searches the ChromaDB collection for similar messages.
    Returns a list of similar documents.
    """
    where_filter = {"topic": topic} if topic else {}

    results = collection.query(
        query_embeddings=[query_embedding],
        n_results=n_results,
        where=where_filter
    )

    # Format results nicely (extract relevant info)
    formatted_results = []
    if results and results.get('ids'):
        for i in range(len(results['ids'][0])):
            formatted_results.append({
                "id": results['ids'][0][i],
                "content": results['documents'][0][i],
                "distance": results['distances'][0][i],
                "metadata": results['metadatas'][0][i]
            })
    print(f"Found {len(formatted_results)} similar messages.")
    return formatted_results

def get_all_messages(skip: int = 0, limit: int = 100, topic: str = None) -> List[Dict]:
    """
    Retrieve all messages from the ChromaDB collection with pagination and optional topic filtering.
    """
    try:
        where_filter = {"topic": topic} if topic else None

        # Get all documents with optional filtering
        results = collection.get(
            where=where_filter,
            limit=limit,
            offset=skip,
            include=["documents", "metadatas"]
        )

        messages = []
        for i, doc_id in enumerate(results["ids"]):
            message = {
                "id": doc_id,
                "content": results["documents"][i],
                "role": results["metadatas"][i].get("role", "unknown"),
                "timestamp": results["metadatas"][i].get("timestamp"),
                "topic": results["metadatas"][i].get("topic")
            }
            messages.append(message)

        return messages
    except Exception as e:
        print(f"Error retrieving messages: {e}")
        return []

def update_message_content(message_id: str, new_content: str, topic: str = None) -> bool:
    """
    Update the content of a specific message in the ChromaDB collection.
    """
    try:
        # Get the existing message to preserve metadata
        existing = collection.get(ids=[message_id], include=["metadatas"])
        if not existing["ids"]:
            return False

        # Update metadata if topic is provided
        metadata = existing["metadatas"][0]
        if topic is not None:
            metadata["topic"] = topic

        # Generate new embedding for the updated content
        from memory.embedding_generator import generate_embedding
        new_embedding = generate_embedding(new_content)

        # Update the document
        collection.update(
            ids=[message_id],
            documents=[new_content],
            embeddings=[new_embedding],
            metadatas=[metadata]
        )

        print(f"Updated message {message_id}")
        return True
    except Exception as e:
        print(f"Error updating message {message_id}: {e}")
        return False

def delete_message_by_id(message_id: str) -> bool:
    """
    Delete a specific message from the ChromaDB collection.
    """
    try:
        collection.delete(ids=[message_id])
        print(f"Deleted message {message_id}")
        return True
    except Exception as e:
        print(f"Error deleting message {message_id}: {e}")
        return False

def get_all_topics() -> List[str]:
    """
    Get a list of all unique topics in the memory database.
    """
    try:
        # Get all documents with metadata
        results = collection.get(include=["metadatas"])

        topics = set()
        for metadata in results["metadatas"]:
            topic = metadata.get("topic")
            if topic:
                topics.add(topic)

        return sorted(list(topics))
    except Exception as e:
        print(f"Error retrieving topics: {e}")
        return []

# Example Usage (for testing)
if __name__ == "__main__":
    # Requires generate_embedding from embedding_generator.py
    from embedding_generator import generate_embedding

    # Add some messages
    msg1_content = "Discuss the benefits of renewable energy."
    msg1_embedding = generate_embedding(msg1_content)
    add_message_to_memory(msg1_content, "user", msg1_embedding, topic="energy")

    msg2_content = "Solar power is a key form of renewable energy."
    msg2_embedding = generate_embedding(msg2_content)
    add_message_to_memory(msg2_content, "assistant", msg2_embedding, topic="energy")

    msg3_content = "Explain the concept of photosynthesis."
    msg3_embedding = generate_embedding(msg3_content)
    add_message_to_memory(msg3_content, "user", msg3_embedding, topic="biology")

    # Search for similar messages
    query = "Tell me about solar energy."
    query_embedding = generate_embedding(query)
    similar_messages = search_memory(query_embedding, n_results=2, topic="energy")

    print("\nSearch Results:")
    for msg in similar_messages:
        print(f"- Distance: {msg['distance']:.4f}, Content: {msg['content']}, Role: {msg['metadata']['role']}, Topic: {msg['metadata'].get('topic')}")

    # Search without topic filter
    query_general = "What did we talk about energy?"
    query_embedding_general = generate_embedding(query_general)
    similar_messages_general = search_memory(query_embedding_general, n_results=3)

    print("\nGeneral Search Results:")
    for msg in similar_messages_general:
         print(f"- Distance: {msg['distance']:.4f}, Content: {msg['content']}, Role: {msg['metadata']['role']}, Topic: {msg['metadata'].get('topic')}")
