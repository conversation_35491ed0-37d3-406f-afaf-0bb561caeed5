"use client";

import React, { useState, useCallback } from 'react';

interface ChatInputProps {
    onSendMessage: (message: string) => void;
    isWebSearchEnabled: boolean;
    onToggleWebSearch: (checked: boolean) => void;
    disabled: boolean;
}

export function ChatInput({ onSendMessage, isWebSearchEnabled, onToggleWebSearch, disabled }: ChatInputProps) {
    const [input, setInput] = useState('');

    // Handle form submission
    const handleSubmit = useCallback((e: React.FormEvent) => {
        e.preventDefault(); // Prevent default form submission
        if (disabled || !input.trim()) return; // Block if disabled or input is empty

        console.log("ChatInput: Submitting message:", input);
        onSendMessage(input.trim());
        setInput(''); // Clear input after submission
    }, [input, disabled, onSendMessage]);

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setInput(e.target.value);
    };

    // Handle web search toggle
    const handleToggleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onToggleWebSearch(e.target.checked);
    };

    // Handle key press (e.g., Enter to submit)
    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault(); // Prevent newline or form submission
            handleSubmit(e as any); // Trigger submission
        }
    };

    return (
        <div className="flex flex-col w-full p-4 bg-white border-t border-gray-200">
            <form onSubmit={handleSubmit} className="flex items-center space-x-2">
                <input
                    type="text"
                    value={input}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyPress}
                    placeholder="Type your message..."
                    className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={disabled}
                />
                <button
                    type="submit"
                    disabled={disabled || !input.trim()}
                    className={`p-2 rounded-lg ${disabled || !input.trim() ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'
                        }`}
                >
                    Send
                </button>
            </form>
            <div className="flex items-center mt-2">
                <label className="flex items-center space-x-2 text-sm">
                    <input
                        type="checkbox"
                        checked={isWebSearchEnabled}
                        onChange={handleToggleChange}
                        disabled={disabled}
                        className="form-checkbox"
                    />
                    <span>Enable web search</span>
                </label>
            </div>
        </div>
    );
}