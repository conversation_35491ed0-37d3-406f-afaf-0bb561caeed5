"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  History,
  Settings,
  Menu,
  X,
  BookOpen,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

export function Sidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const pathname = usePathname();

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const toggleMobileSidebar = () => {
    setMobileOpen(!mobileOpen);
  };

  const navItems = [
    { href: '/', icon: Home, label: 'Home' },
    { href: '/memory', icon: History, label: 'Memories' },
    { href: '/docs', icon: BookOpen, label: 'Documentation' },
    { href: '/settings', icon: Settings, label: 'Settings' },
    { href: '/help', icon: HelpCircle, label: 'Help' },
  ];

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <>
      {/* Mobile menu button */}
      <button 
        onClick={toggleMobileSidebar}
        className="fixed top-4 left-4 z-40 md:hidden bg-white p-2 rounded-md shadow-md"
        aria-label="Toggle menu"
      >
        {mobileOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Mobile overlay */}
      {mobileOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={() => setMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div 
        className={cn(
          "fixed top-0 left-0 h-full z-30 transition-all duration-300 ease-in-out",
          collapsed ? "w-16" : "w-64",
          mobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}
      >
        <div className="flex flex-col h-full border-r bg-gray-100">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            {!collapsed && (
              <h1 className="text-xl font-bold">MindChat</h1>
            )}
            <button 
              onClick={toggleSidebar}
              className="p-1 rounded-md hover:bg-gray-200 hidden md:block"
              aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {collapsed ? <Menu size={20} /> : <X size={20} />}
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-grow p-4 space-y-2">
            {navItems.map((item) => (
              <Link 
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center p-2 rounded-md transition-colors",
                  isActive(item.href) 
                    ? "bg-blue-100 text-blue-700" 
                    : "text-gray-700 hover:bg-gray-200",
                  collapsed && "justify-center"
                )}
              >
                <item.icon size={20} className={collapsed ? "" : "mr-2"} />
                {!collapsed && <span>{item.label}</span>}
              </Link>
            ))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t">
            {!collapsed && (
              <div className="text-xs text-gray-500">
                <p>MindChat v1.0.0</p>
                <p>© 2023 MindChat</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content margin when sidebar is expanded */}
      <div 
        className={cn(
          "transition-all duration-300 ease-in-out md:ml-64",
          collapsed && "md:ml-16"
        )}
      />
    </>
  );
}