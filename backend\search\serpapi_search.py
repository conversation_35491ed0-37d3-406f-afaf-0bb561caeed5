# mindchat/backend/search/serpapi_search.py
import os
from serpapi import GoogleSearch
from typing import Dict, List, Any

from shared.config import SERPAPI_API_KEY

def perform_serpapi_search(query: str, num_results: int = 5) -> List[Dict[str, Any]]:
    """
    Performs a web search using SerpAPI.
    Returns a list of search results.
    """
    if not SERPAPI_API_KEY:
        print("SerpAPI key is not configured.")
        return []

    try:
        # Initialize GoogleSearch with your API key
        search = GoogleSearch({
            "q": query,           # The search query
            "api_key": SERPAPI_API_KEY, # Your SerpAPI key
            "num": num_results,   # Number of results to fetch
            "hl": "en",           # Host Language
            "gl": "us"            # Geo Location (adjust as needed)
        })

        # Get the search results as a dictionary
        results = search.get_dict()

        # Extract relevant information (e.g., organic results)
        # The structure of the results dictionary can vary based on the search type
        # We'll focus on 'organic_results' which are the standard web links
        organic_results = results.get("organic_results", [])

        # You might also be interested in other types of results like 'answer_box', 'knowledge_graph', etc.
        # For now, we'll just return the organic results.

        # Format the results into a list of dictionaries
        formatted_results = []
        for res in organic_results:
            formatted_results.append({
                "title": res.get("title"),
                "link": res.get("link"),
                "snippet": res.get("snippet"),
                # Add other fields if needed, e.g., "displayed_link"
            })

        print(f"SerpAPI search for '{query}' successful. Found {len(formatted_results)} organic results.")
        return formatted_results

    except Exception as e:
        print(f"Error during SerpAPI search for '{query}': {e}")
        return []

# Example Usage (for testing)
if __name__ == "__main__":
    # You would set the environment variable before running this script
    # export SERPAPI_API_KEY="your_serpapi_key"
    # (or using set on Windows)

    if SERPAPI_API_KEY:
        search_query = "latest AI research"
        search_results = perform_serpapi_search(search_query, num_results=3)
        print("\nSerpAPI Search Results:")
        for i, res in enumerate(search_results):
            print(f"{i+1}. Title: {res.get('title')}")
            print(f"   Link: {res.get('link')}")
            print(f"   Snippet: {res.get('snippet')}")
            print("-" * 20)
    else:
        print("\nSerpAPI key not set. Cannot run test.")
