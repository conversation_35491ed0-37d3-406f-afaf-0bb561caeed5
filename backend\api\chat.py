# mindchat/backend/api/chat.py
import os
import json
import re

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import uuid4

# Import shared schema
from shared.schema import Message, StoredMessage, ChatResponse, RetrievedMemory, SearchResult
from shared.config import LLM_MODEL_NAME

# Import memory components
from memory.embedding_generator import generate_embedding
from memory.chromadb_interface import add_message_to_memory, search_memory

# Import the OpenAI-compatible adapter
from llm_providers.openai_compatible import get_openai_compatible_response
from llm_providers.mock_llm import get_mock_response

# Import web search agent components
from search.web_search_agent import should_perform_search, get_search_query, perform_web_search, process_search_results

router = APIRouter()

# --- NEW: Chart Detection & Generation Functions ---
def should_generate_chart(prompt: str) -> bool:
    """Detect if user wants a chart/graph"""
    chart_keywords = [
        'chart', 'graph', 'plot', 'bar chart', 'line chart', 'pie chart',
        'histogram', 'data visualization', 'show data', 'visualize',
        'statistics', 'trends', 'comparison', 'doughnut chart', 'radar chart'
    ]
    return any(keyword in prompt.lower() for keyword in chart_keywords)

def extract_chart_config(llm_response: str) -> tuple[str, dict | None]:
    """Extract Chart.js config from LLM response"""
    chart_match = re.search(r'```chartjs\n(.*?)\n```', llm_response, re.DOTALL)
    if chart_match:
        try:
            config = json.loads(chart_match.group(1))
            # Remove the chartjs block from response
            clean_response = llm_response.replace(chart_match.group(0), '').strip()
            print(f"Chart config extracted: {config.get('type', 'unknown')} chart")
            return clean_response, config
        except json.JSONDecodeError as e:
            print(f"Error parsing Chart.js config: {e}")
            pass
    return llm_response, None

# --- Enhanced LLM Adapter Layer ---
def call_llm_adapter(model: str, prompt: str, history: List[Message], memory_context: List[RetrievedMemory], search_context: Optional[str], should_chart: bool = False) -> str:
    """
    Abstracted function to call the appropriate LLM provider.
    Now includes chart generation capabilities.
    """
    print(f"Calling LLM Adapter for model: {model}")
    
    messages_for_api = []

    # --- Enhanced System Message with Chart Instructions ---
    system_message_content = (
        "You are a helpful and informative AI assistant. "
        "Your goal is to answer the user's question accurately and comprehensively, "
        "integrating information from provided sources.\n\n"
    )
    
    # --- Mermaid Diagram Instructions ---
    system_message_content += (
        "If the user explicitly asks for a diagram, flowchart, or visualization, or if the topic is well-suited for a Mermaid diagram (e.g., workflows, relationships, concepts), "
        "generate the Mermaid syntax directly in your response. "
        "Wrap the Mermaid code within '```mermaid\\n...\\n```' markers. "
        "**Do not include any other text outside the mermaid code block if you are generating a diagram.** "
        "If you cannot generate a diagram, respond with text as usual.\n\n"
    )
 
    # Add this to your chat.py system message for better Mermaid generation:

    system_message_content += (
    "When generating Mermaid diagrams, follow these strict rules:\n"
    "- Use simple node labels without special characters like |, &, <, >\n"
    "- Each statement must be on a separate line\n"
    "- Use only basic node shapes: [] for rectangles, () for circles, {} for rhombus\n"
    "- Keep labels short and simple\n"
    "- Example format:\n"
    "```mermaid\n"
    "flowchart TD\n"
    "    A[Start]\n"
    "    B[Process]\n"
    "    C{Decision}\n"
    "    D[End]\n"
    "    A --> B\n"
    "    B --> C\n"
    "    C --> D\n"
    "```\n\n"
)

# Add this to your system message in chat.py:

    system_message_content += (
    "When generating Mermaid diagrams, follow these STRICT syntax rules:\n"
    "- Use simple node labels without special characters\n"
    "- Do NOT use labels on arrows (no text after --> or ==>)\n"
    "- Each statement must be on a separate line\n"
    "- Use only basic node shapes: [] for rectangles, () for rounded, {} for diamond\n"
    "- NO quotes or pipes in any labels\n"
    "- Example CORRECT format:\n"
    "```mermaid\n"
    "flowchart TD\n"
    "    A[Start Process]\n"
    "    B[Collect Data]\n" 
    "    C{Decision Point}\n"
    "    D[End Process]\n"
    "    A --> B\n"
    "    B --> C\n"
    "    C --> D\n"
    "```\n"
    "WRONG examples to AVOID:\n"
    "- A -->\"text\" B (no quotes on arrows)\n"
    "- A -->|text| B (no pipes on arrows)\n"
    "- A[\"complex text\"] (no quotes in labels)\n\n"
)


    # --- NEW: Chart.js Instructions ---
# Update the system message in chat.py to be more specific about JSON format:

    system_message_content += (
    "If the user asks for charts, graphs, or data visualization, "
    "generate a Chart.js configuration object wrapped in '```chartjs\\n...\\n```' markers. "
    "Supported chart types: line, bar, pie, doughnut, radar, scatter. "
    "Always use VALID JSON FORMAT with double quotes and proper escaping. "
    "Use appropriate colors and make charts visually appealing.\n"
    "Example format (EXACT JSON SYNTAX REQUIRED):\n"
    "```chartjs\n"
    "{\n"
    '  "type": "bar",\n'
    '  "data": {\n'
    '    "labels": ["Q1", "Q2", "Q3", "Q4"],\n'
    '    "datasets": [\n'
    '      {\n'
    '        "label": "Sales ($000)",\n'
    '        "data": [12, 19, 3, 5],\n'
    '        "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"],\n'
    '        "borderColor": "#ffffff",\n'
    '        "borderWidth": 2\n'
    '      }\n'
    '    ]\n'
    '  },\n'
    '  "options": {\n'
    '    "responsive": true,\n'
    '    "plugins": {\n'
    '      "title": {\n'
    '        "display": true,\n'
    '        "text": "Quarterly Sales"\n'
    '      }\n'
    '    }\n'
    '  }\n'
    "}\n"
    "```\n"
    "IMPORTANT: Use only double quotes, no single quotes. No trailing commas. No comments.\n\n"
)
# Update the chart generation instructions in your system message:

    system_message_content += (
    "When generating Chart.js configurations, use Chart.js v3+ syntax:\n"
    "- Use 'scales: { x: {}, y: {} }' instead of 'scales: { xAxes: [], yAxes: [] }'\n"
    "- Always include proper data structure with labels and datasets\n"
    "Example Chart.js v3+ format:\n"
    "```chartjs\n"
    "{\n"
    '  "type": "bar",\n'
    '  "data": {\n'
    '    "labels": ["Q1", "Q2", "Q3", "Q4"],\n'
    '    "datasets": [{\n'
    '      "label": "Sales",\n'
    '      "data": [12000, 19000, 15000, 22000],\n'
    '      "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0"]\n'
    '    }]\n'
    '  },\n'
    '  "options": {\n'
    '    "responsive": true,\n'
    '    "plugins": {\n'
    '      "title": {\n'
    '        "display": true,\n'
    '        "text": "Quarterly Sales"\n'
    '      }\n'
    '    },\n'
    '    "scales": {\n'
    '      "x": {\n'
    '        "display": true\n'
    '      },\n'
    '      "y": {\n'
    '        "display": true,\n'
    '        "beginAtZero": true\n'
    '      }\n'
    '    }\n'
    '  }\n'
    "}\n"
    "```\n\n"
)


# Add this to your chat.py system message:

    system_message_content += (
    "For complex charts with large datasets (>50 data points), consider:\n"
    "- Using aggregated or sampled data for better performance\n"
    "- Adding data labels only for key points\n"
    "- Using appropriate colors and gradients\n"
    "- Including meaningful titles and legends\n"
    "Example complex chart:\n"
    "```chartjs\n"
    "{\n"
    '  \"type\": \"line\",\n'
    '  \"data\": {\n'
    '    \"labels\": [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\"],\n'
    '    \"datasets\": [\n'
    '      {\n'
    '        \"label\": \"Revenue\",\n'
    '        \"data\": [65000, 75000, 70000, 82000, 79000, 88000],\n'
    '        \"borderColor\": \"rgb(75, 192, 192)\",\n'
    '        \"backgroundColor\": \"rgba(75, 192, 192, 0.1)\",\n'
    '        \"fill\": true\n'
    '      },\n'
    '      {\n'
    '        \"label\": \"Expenses\",\n'
    '        \"data\": [45000, 52000, 48000, 61000, 55000, 67000],\n'
    '        \"borderColor\": \"rgb(255, 99, 132)\",\n'
    '        \"backgroundColor\": \"rgba(255, 99, 132, 0.1)\",\n'
    '        \"fill\": true\n'
    '      }\n'
    '    ]\n'
    '  },\n'
    '  \"options\": {\n'
    '    \"responsive\": true,\n'
    '    \"plugins\": {\n'
    '      \"title\": {\n'
    '        \"display\": true,\n'
    '        \"text\": \"Financial Performance Over Time\"\n'
    '      }\n'
    '    }\n'
    '  }\n'
    "}\n"
    "```\n\n"
)

    # Add memory context if available
    if memory_context:
        system_message_content += (
            "Relevant snippets from past conversations are provided below, marked as 'Memory Snippets:'. "
            "Use these to recall previous discussions and maintain conversational continuity. "
            "However, for questions requiring up-to-date information, prioritize recent search results.\n\n"
            "Memory Snippets:\n"
        )
        for mem in memory_context:
            system_message_content += f"[{mem.metadata.get('topic', 'No Topic')}] {mem.content}\n"
        system_message_content += "---\n\n"

    # Add search context if available
    if search_context:
        system_message_content += (
             "Based on the following search results, answer the user's question. "
             "Use *only* the information from these search results if possible. "
             "If the search results do not contain enough information, state that you couldn't find the answer in the provided sources.\n\n"
             "Search Results:\n"
        )
        system_message_content += search_context
        system_message_content += "---\n\n"

    # Add final instruction based on contexts
    if memory_context and search_context:
         system_message_content += "Synthesize information from both Memory Snippets and Search Results to provide the best answer, prioritizing Search Results for current or factual queries.\n\n"
    elif memory_context:
         system_message_content += "Answer the user's question based on the provided Memory Snippets and your general knowledge.\n\n"
    elif search_context:
         system_message_content += "Answer the user's question based on the provided Search Results and your general knowledge.\n\n"
    else:
         system_message_content += "Answer the user's question based on your general knowledge.\n\n"

    # --- Add chart-specific instruction if needed ---
    if should_chart:
        system_message_content += (
            "The user's query appears to be asking for data visualization. "
            "Please generate appropriate Chart.js configuration for the data mentioned. "
            "Make sure to use realistic or representative data if none is provided.\n\n"
        )

    system_message_content += (
    "Supported chart types: line, bar, pie, doughnut, radar, scatter, matrix. " # Removed 'heatmap' directly, kept 'matrix'
    "**For heatmap-like visualizations, use the 'matrix' chart type.** " # Explicit instruction
    "Always use VALID JSON FORMAT with double quotes and proper escaping. "
    "Use appropriate colors and make charts visually appealing.\n"
    "Example Chart.js v3+ format:\n"
    "```chartjs\n"
    "{\n"
    '  "type": "bar",\n' # Existing example
    # ... other examples ...
    "}\n"
    "```\n\n"
)

# ADD A NEW EXAMPLE FOR MATRIX (HEATMAP) CHARTS:
    system_message_content += (
    "Example for 'matrix' chart (for heatmap-like data, where 'x' and 'y' are categories, and 'v' is the value for color):\n"
    "```chartjs\n"
    "{\n"
    '  "type": "matrix",\n'
    '  "data": {\n'
    '    "datasets": [{\n'
    '      "label": "Sentiment Score",\n'
    '      "data": [\n'
    '        {"x": "Model A", "y": "Group 1", "v": 0.8, "r": 10},\n'
    '        {"x": "Model B", "y": "Group 1", "v": 0.3, "r": 10},\n'
    '        {"x": "Model C", "y": "Group 1", "v": 0.6, "r": 10},\n'
    '        {"x": "Model A", "y": "Group 2", "v": 0.2, "r": 10},\n'
    '        {"x": "Model B", "y": "Group 2", "v": 0.7, "r": 10},\n'
    '        {"x": "Model C", "y": "Group 2", "v": 0.4, "r": 10}\n'
    '      ],\n'
    '      "backgroundColor": function(context) {\n'
    '        const value = context.raw.v;\n'
    '        if (value < 0.3) return \'rgba(255, 99, 132, 0.7)\'; // Red\n'
    '        if (value < 0.6) return \'rgba(255, 206, 86, 0.7)\'; // Yellow\n'
    '        return \'rgba(75, 192, 192, 0.7)\'; // Green\n'
    '      },\n'
    '      "borderColor": "#fff",\n'
    '      "borderWidth": 1,\n'
    '      "width": function(context) { return (context.chart.chartArea.width / 3) - 1; },\n'
    '      "height": function(context) { return (context.chart.chartArea.height / 2) - 1; }\n'
    '    }]\n'
    '  },\n'
    '  "options": {\n'
    '    "responsive": true,\n'
    '    "plugins": {\n'
    '      "title": {\n'
    '        "display": true,\n'
    '        "text": "Sentiment Analysis Heatmap Example"\n'
    '      },\n'
    '      "legend": {\n'
    '        "display": false\n'
    '      }\n'
    '    },\n'
    '    "scales": {\n'
    '      "x": {\n'
    '        "type": "category",\n'
    '        "labels": ["Model A", "Model B", "Model C"],\n'
    '        "grid": { "display": false }\n'
    '      },\n'
    '      "y": {\n'
    '        "type": "category",\n'
    '        "labels": ["Group 1", "Group 2"],\n'
    '        "grid": { "display": false }\n'
    '      }\n'
    '    }\n'
    '  }\n'
    "}\n"
    "```\n\n"
)
        # --- Add System Message to API Call List ---
    messages_for_api.append({"role": "system", "content": system_message_content})

    # --- Add History and Latest Prompt ---
    for msg in history:
         messages_for_api.append({"role": msg.role, "content": msg.content})

    # Add the latest user message
    messages_for_api.append({"role": "user", "content": prompt})

    # --- Make API Call ---
    try:
        # Check if API keys are configured
        openai_api_key = os.environ.get("OPENAI_API_KEY")
        openai_base_url = os.environ.get("OPENAI_BASE_URL")

        if not openai_api_key and not openai_base_url:
            print("No API keys configured, using mock LLM for testing")
            chat_completion = get_mock_response(model, messages_for_api)
        else:
            chat_completion = get_openai_compatible_response(model, messages_for_api)

        # --- Check for Mermaid Syntax in Response ---
        if '```mermaid' in chat_completion:
             print("Mermaid syntax detected in LLM response.")

        # --- NEW: Check for Chart.js config in Response ---
        if '```chartjs' in chat_completion:
            print("Chart.js config detected in LLM response.")

        assistant_response = chat_completion
        return assistant_response

    except ValueError as e:
        print(f"LLM configuration error: {e}")
        raise HTTPException(status_code=500, detail=f"LLM configuration error: {e}")
    except Exception as e:
        print(f"Error during LLM API call: {e}")
        raise HTTPException(status_code=500, detail=f"Error calling LLM provider: {e}")


@router.post("/chat", response_model=ChatResponse)
async def process_chat_message(messages: List[Message]):
    """
    Receives chat history, processes user message, performs memory search and
    conditional web search, sends context to LLM, and stores messages.
    Returns assistant message and retrieved memory/search results.
    Now supports Chart.js generation.
    """
    if not messages:
        raise HTTPException(status_code=400, detail="No messages provided")

    latest_message = messages[-1]
    if latest_message.role != 'user':
         raise HTTPException(status_code=400, detail="Latest message must be from user")

    prompt = latest_message.content
    history = messages[:-1]

    # --- NEW: Chart Detection ---
    should_chart = should_generate_chart(prompt)
    if should_chart:
        print(f"Chart generation detected for prompt: {prompt}")

    # --- Memory Integration ---
    query_embedding = None
    try:
        query_embedding = generate_embedding(prompt)
    except Exception as e:
         print(f"Error generating embedding for search: {e}")

    retrieved_memory_raw: List[Dict] = []
    if query_embedding:
        try:
             retrieved_memory_raw = search_memory(query_embedding, n_results=3)
        except Exception as e:
             print(f"Error searching ChromaDB: {e}")
             retrieved_memory_raw = []

    retrieved_memory_objects = [RetrievedMemory(**item) for item in retrieved_memory_raw]

    # --- Web Search Integration ---
    search_results_raw: List[Dict[str, Any]] = []
    formatted_search_context: Optional[str] = None

    web_search_explicitly_enabled = latest_message.web_search_enabled is True

    if web_search_explicitly_enabled and should_perform_search(prompt):
        print("Web search is explicitly enabled and prompt requires a search.")
        search_query = get_search_query(prompt)
        if search_query:
            try:
                search_results_raw = perform_web_search(search_query, num_results=5)
                if search_results_raw:
                     formatted_search_context = process_search_results(search_results_raw)
                else:
                    print("Web search performed but returned no results.")
            except Exception as e:
                 print(f"An error occurred during the web search process: {e}")

    search_result_objects = [SearchResult(**item) for item in search_results_raw] if search_results_raw else []

    # --- Enhanced LLM Call with Chart Support ---
    chosen_model = LLM_MODEL_NAME

    try:
        # Pass chart detection flag to LLM adapter
        llm_response_content = call_llm_adapter(
            chosen_model, 
            prompt, 
            history, 
            retrieved_memory_objects, 
            formatted_search_context,
            should_chart
        )

        # --- NEW: Process Chart Config if present ---
        final_response_content, chart_config = extract_chart_config(llm_response_content)
        
        if chart_config:
            print(f"Chart configuration extracted: {chart_config.get('type', 'unknown')} chart with {len(chart_config.get('data', {}).get('datasets', []))} datasets")
            # The chart config will be handled by the frontend
            # We keep the chart block in the response for frontend parsing
            assistant_message_content = llm_response_content
        else:
            assistant_message_content = final_response_content

        assistant_message = Message(role="assistant", content=assistant_message_content)

        # Check response types for logging
        is_diagram = '```mermaid' in assistant_message_content and assistant_message_content.strip().startswith('```mermaid')
        is_chart = '```chartjs' in assistant_message_content
        
        if is_diagram:
             print("LLM response detected as a Mermaid diagram.")
        if is_chart:
             print("LLM response detected as a Chart.js visualization.")

    except HTTPException:
         raise
    except Exception as e:
        print(f"Unexpected error calling LLM adapter: {e}")
        raise HTTPException(status_code=500, detail=f"Unexpected error during LLM call: {e}")

    # --- Store Messages in Memory ---
    try:
        user_msg_embedding = generate_embedding(latest_message.content)
        add_message_to_memory(latest_message.content, latest_message.role, user_msg_embedding)

        # Store the assistant message content (might contain chart config)
        assistant_msg_embedding = generate_embedding(assistant_message.content)
        add_message_to_memory(assistant_message.content, assistant_message.role, assistant_msg_embedding)

    except Exception as e:
         print(f"Error storing messages in memory: {e}")

    # --- Construct and Return Response ---
    return ChatResponse(
        assistant_message=assistant_message,
        retrieved_memory=retrieved_memory_objects,
        raw_search_results=search_result_objects
    )
