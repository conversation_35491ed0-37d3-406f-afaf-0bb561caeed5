"use client";

import { useState, useCallback, useRef } from 'react';
import debounce from 'lodash/debounce';
import { ChatInput } from '@/components/chat-input';
import { ChatMessage } from '@/components/chat-message';

// Define interfaces
interface RetrievedMemory {
    id: string;
    content: string;
    distance: number;
    metadata: { [key: string]: any };
}

interface SearchResult {
    title?: string;
    link?: string;
    snippet?: string;
}

interface Message {
    role: 'user' | 'assistant';
    content: string;
    timestamp?: string;
    web_search_enabled?: boolean;
    retrievedMemory?: RetrievedMemory[];
    searchResults?: SearchResult[];
    id?: string;
}

interface ChatResponse {
    assistant_message: Message;
    retrieved_memory: RetrievedMemory[];
    raw_search_results: SearchResult[];
}

export default function HomePage() {
    const [messages, setMessages] = useState<Message[]>([]);
    const [loading, setLoading] = useState(false);
    const [isWebSearchEnabled, setIsWebSearchEnabled] = useState(true);
    const isProcessing = useRef(false);
    const processedMessages = useRef<Set<string>>(new Set());
    const submissionLock = useRef<boolean>(false);

    const handleSendMessage = useCallback(debounce(async (message: string) => {
        if (submissionLock.current || isProcessing.current || loading) {
            console.log("Submission blocked: Lock active or processing", { message });
            return;
        }

        const now = Date.now();
        const messageId = `${now}-${Math.random().toString(36).substr(2, 9)}`;
        const messageKey = `${message.trim()}-${isWebSearchEnabled}`;

        if (processedMessages.current.has(messageKey)) {
            console.log("Submission blocked: Duplicate content", { messageId, message });
            return;
        }

        submissionLock.current = true;
        isProcessing.current = true;
        processedMessages.current.add(messageKey);
        console.log("Processing message:", message, { messageId });
        console.log("Current messages state BEFORE optimistic update:", messages);

        const newUserMessage: Message = {
            role: 'user',
            content: message,
            web_search_enabled: isWebSearchEnabled,
            id: messageId,
            timestamp: new Date().toISOString(),
        };

        let updatedMessages: Message[] = [];
        setMessages(prevMessages => {
            if (prevMessages.some(msg =>
                msg.id === messageId ||
                (msg.role === 'user' && msg.content === message && msg.timestamp && (now - new Date(msg.timestamp).getTime()) < 1000)
            )) {
                console.log("Duplicate user message detected, skipping:", message, { messageId });
                setTimeout(() => {
                    submissionLock.current = false;
                    isProcessing.current = false;
                    processedMessages.current.delete(messageKey);
                }, 100);
                return prevMessages;
            }
            console.log("State inside first setMessages (optimistic):", prevMessages, newUserMessage);
            updatedMessages = [...prevMessages, newUserMessage];
            return updatedMessages;
        });

        if (!updatedMessages.length) {
            console.log("No messages to send, aborting request", { messageId });
            setTimeout(() => {
                submissionLock.current = false;
                isProcessing.current = false;
                processedMessages.current.delete(messageKey);
            }, 100);
            return;
        }

        setLoading(true);

        try {
            console.log("Payload sent to backend:", JSON.stringify(updatedMessages));
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/chat`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updatedMessages),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend error: ${response.status} - ${errorData.detail || response.statusText}`);
            }

            const chatResponse: ChatResponse = await response.json();
            console.log("Received chat response from backend:", chatResponse);

            const assistantMessage = {
                ...chatResponse.assistant_message,
                id: `${messageId}-assistant`,
                timestamp: new Date().toISOString(),
            };
            const retrievedMemory = chatResponse.retrieved_memory;
            const rawSearchResults = chatResponse.raw_search_results;

            const assistantMessageWithMemoryAndSearch: Message = {
                ...assistantMessage,
                retrievedMemory,
                searchResults: rawSearchResults,
            };

            setMessages(prevMessages => {
                if (prevMessages.some(msg => msg.id === assistantMessageWithMemoryAndSearch.id)) {
                    console.log("Duplicate assistant message detected, skipping:", assistantMessageWithMemoryAndSearch);
                    return prevMessages;
                }
                console.log("State inside second setMessages (add assistant):", prevMessages, assistantMessageWithMemoryAndSearch);
                return [...prevMessages, assistantMessageWithMemoryAndSearch];
            });
        } catch (error) {
            console.error('Error sending message to backend:', error);
            setMessages(prevMessages => [
                ...prevMessages,
                {
                    role: 'assistant',
                    content: `Error: Could not get a response from the backend. ${error instanceof Error ? error.message : String(error)}`,
                    id: `${messageId}-error`,
                    timestamp: new Date().toISOString(),
                },
            ]);
        } finally {
            setLoading(false);
            isProcessing.current = false;
            submissionLock.current = false;
            setTimeout(() => processedMessages.current.delete(messageKey), 1000);
        }
    }, 200), [isWebSearchEnabled, loading, messages]); // Fixed: Added missing closing parenthesis


    const handleToggleWebSearch = (checked: boolean) => {
        setIsWebSearchEnabled(checked);
    };

    return (
        <div className="flex flex-col h-screen">
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((msg) => (
                    <ChatMessage key={msg.id || `${msg.role}-${msg.content}-${Math.random()}`} message={msg} />
                ))}
                {loading && (
                    <div className="flex items-center">
                        <div className="max-w-xs px-4 py-2 text-black bg-gray-200 rounded-lg">
                            Loading...
                        </div>
                    </div>
                )}
            </div>
            <ChatInput
                onSendMessage={handleSendMessage}
                isWebSearchEnabled={isWebSearchEnabled}
                onToggleWebSearch={handleToggleWebSearch}
                disabled={loading || isProcessing.current}
            />
        </div>
    );
}