{"version": 2, "name": "mindchat", "builds": [{"src": "mindchat-frontend/package.json", "use": "@vercel/next"}, {"src": "backend/main.py", "use": "@vercel/python"}], "routes": [{"src": "/api/(.*)", "dest": "backend/main.py"}, {"src": "/(.*)", "dest": "mindchat-frontend/$1"}], "env": {"OPENAI_API_KEY": "@openai_api_key", "LLM_MODEL_NAME": "@llm_model_name", "SERPAPI_API_KEY": "@serpapi_api_key", "BRAVE_API_KEY": "@brave_api_key", "HF_HUB_DISABLE_SYMLINKS_WARNING": "1"}, "build": {"env": {"PYTHONPATH": "backend"}}, "functions": {"backend/main.py": {"runtime": "python3.9"}}}