# mindchat/backend/llm_providers/mock_llm.py
from typing import List, Dict, Any
import random

def get_mock_response(model: str, messages: List[Dict[str, Any]]) -> str:
    """
    Mock LLM provider for testing without API keys.
    Returns predefined responses based on user input.
    """
    
    # Get the latest user message
    user_messages = [msg for msg in messages if msg.get('role') == 'user']
    if not user_messages:
        return "Hello! I'm a mock AI assistant. How can I help you today?"
    
    latest_message = user_messages[-1].get('content', '').lower()
    
    # Chart/visualization requests
    if any(word in latest_message for word in ['chart', 'graph', 'plot', 'visualize']):
        return """I'll create a sample chart for you!

```chartjs
{
  "type": "bar",
  "data": {
    "labels": ["January", "February", "March", "April", "May"],
    "datasets": [{
      "label": "Sample Data",
      "data": [12, 19, 3, 5, 2],
      "backgroundColor": [
        "#FF6384",
        "#36A2EB", 
        "#FFCE56",
        "#4BC0C0",
        "#9966FF"
      ],
      "borderColor": "#ffffff",
      "borderWidth": 2
    }]
  },
  "options": {
    "responsive": true,
    "plugins": {
      "title": {
        "display": true,
        "text": "Sample Chart Generated by Mock AI"
      }
    },
    "scales": {
      "y": {
        "beginAtZero": true
      }
    }
  }
}
```

This is a sample bar chart showing mock data. In a real implementation, I would generate charts based on your specific data requirements!"""

    # Diagram requests
    elif any(word in latest_message for word in ['diagram', 'flowchart', 'flow', 'process']):
        return """Here's a sample process diagram:

```mermaid
flowchart TD
    A[Start Process] --> B[Collect Input]
    B --> C{Validate Data}
    C -->|Valid| D[Process Data]
    C -->|Invalid| E[Show Error]
    D --> F[Generate Output]
    E --> B
    F --> G[End Process]
```

This diagram shows a typical data processing workflow. I can create more specific diagrams based on your requirements!"""

    # Math questions
    elif any(word in latest_message for word in ['2+2', 'math', 'calculate', 'equals']):
        return "Great question! 2 + 2 = 4. I'm a mock AI assistant, so I can handle basic math and provide helpful responses for testing purposes."
    
    # Greeting responses
    elif any(word in latest_message for word in ['hello', 'hi', 'hey', 'greetings']):
        greetings = [
            "Hello! I'm your mock AI assistant. I'm here to help you test the MindChat application!",
            "Hi there! I'm a test AI assistant. What would you like to explore today?",
            "Greetings! I'm running in mock mode for testing. How can I assist you?",
            "Hey! I'm your friendly test AI. Ready to chat and test some features?"
        ]
        return random.choice(greetings)
    
    # Help requests
    elif any(word in latest_message for word in ['help', 'what can you do', 'capabilities']):
        return """I'm a mock AI assistant for testing MindChat! Here's what I can help you test:

🤖 **Chat Features:**
- Basic conversation and responses
- Memory integration (I can reference past messages)
- Web search integration (when enabled)

📊 **Visualizations:**
- Ask me to create a "chart" or "graph" to test Chart.js integration
- Request a "diagram" or "flowchart" to test Mermaid diagrams

🧠 **Memory Testing:**
- I can remember our conversation through the memory system
- Try asking about something we discussed earlier

🔍 **Search Testing:**
- Enable web search and ask current questions
- I'll attempt to search and provide context

Try asking me to create a chart, diagram, or just have a normal conversation to test the features!"""

    # Default responses
    else:
        responses = [
            f"I understand you said: '{latest_message}'. I'm a mock AI assistant running in test mode. This helps verify that the chat system is working correctly!",
            f"Thanks for your message about '{latest_message}'. I'm here to help test the MindChat application. Everything seems to be working well!",
            f"I received your message: '{latest_message}'. As a test AI, I can confirm the communication between frontend and backend is functioning properly!",
            f"Your message '{latest_message}' has been processed successfully. I'm a mock assistant helping you test the system's capabilities.",
        ]
        return random.choice(responses)
