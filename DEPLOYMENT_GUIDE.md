# MindChat Deployment Guide

## 📋 Overview

This guide covers deployment options for MindChat, from local development to production environments. MindChat is designed to be easily deployable using Docker containers or traditional server setups.

## 🏗️ Architecture Requirements

### Minimum System Requirements
- **CPU**: 2 cores (4 cores recommended)
- **RAM**: 4GB (8GB recommended)
- **Storage**: 10GB (50GB recommended for production)
- **Network**: Stable internet connection for AI API calls

### Software Dependencies
- **Docker & Docker Compose** (recommended)
- **Python 3.9+** (if running without Docker)
- **Node.js 18+** (if running without Docker)
- **Git** (for source code management)

## 🐳 Docker Deployment (Recommended)

### Quick Start with Docker Compose

1. **Clone the Repository**
```bash
git clone <repository-url>
cd mindchat
```

2. **Configure Environment Variables**
```bash
# Copy example environment file
cp .env.example .env

# Edit environment variables
nano .env
```

3. **Start Services**
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

4. **Access Application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

### Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_MODEL_NAME=${LLM_MODEL_NAME}
      - SERPAPI_API_KEY=${SERPAPI_API_KEY}
      - BRAVE_API_KEY=${BRAVE_API_KEY}
    volumes:
      - ./data/chromadb:/app/chromadb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./mindchat-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  chromadb_data:
```

### Environment Configuration

```bash
# .env file
# AI Configuration
OPENAI_API_KEY=your_openai_api_key_here
LLM_MODEL_NAME=gpt-3.5-turbo
OPENAI_API_BASE_URL=https://api.openai.com/v1

# Search Configuration (Optional)
SERPAPI_API_KEY=your_serpapi_key_here
BRAVE_API_KEY=your_brave_api_key_here

# Database Configuration
CHROMADB_PATH=/app/chromadb

# Security Configuration
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Performance Configuration
HF_HUB_DISABLE_SYMLINKS_WARNING=1
```

## 🖥️ Manual Deployment

### Backend Deployment

1. **Setup Python Environment**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure Environment**
```bash
export OPENAI_API_KEY="your_api_key_here"
export LLM_MODEL_NAME="gpt-3.5-turbo"
```

3. **Start Backend Server**
```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Frontend Deployment

1. **Setup Node.js Environment**
```bash
cd mindchat-frontend
npm install
```

2. **Build for Production**
```bash
npm run build
npm start
```

3. **Development Mode**
```bash
npm run dev
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Using AWS ECS with Fargate

1. **Create ECR Repositories**
```bash
aws ecr create-repository --repository-name mindchat-backend
aws ecr create-repository --repository-name mindchat-frontend
```

2. **Build and Push Images**
```bash
# Backend
docker build -t mindchat-backend ./backend
docker tag mindchat-backend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/mindchat-backend:latest
docker push <account-id>.dkr.ecr.<region>.amazonaws.com/mindchat-backend:latest

# Frontend
docker build -t mindchat-frontend ./mindchat-frontend
docker tag mindchat-frontend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/mindchat-frontend:latest
docker push <account-id>.dkr.ecr.<region>.amazonaws.com/mindchat-frontend:latest
```

3. **Create ECS Task Definition**
```json
{
  "family": "mindchat",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "<account-id>.dkr.ecr.<region>.amazonaws.com/mindchat-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "OPENAI_API_KEY",
          "value": "your_api_key_here"
        }
      ]
    },
    {
      "name": "frontend",
      "image": "<account-id>.dkr.ecr.<region>.amazonaws.com/mindchat-frontend:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ]
    }
  ]
}
```

### Google Cloud Platform

#### Using Cloud Run

1. **Deploy Backend**
```bash
gcloud run deploy mindchat-backend \
  --source ./backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars OPENAI_API_KEY=your_api_key_here
```

2. **Deploy Frontend**
```bash
gcloud run deploy mindchat-frontend \
  --source ./mindchat-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars NEXT_PUBLIC_API_URL=https://mindchat-backend-url
```

### Digital Ocean

#### Using App Platform

1. **Create App Spec**
```yaml
# .do/app.yaml
name: mindchat
services:
- name: backend
  source_dir: /backend
  github:
    repo: your-username/mindchat
    branch: main
  run_command: uvicorn main:app --host 0.0.0.0 --port 8080
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: OPENAI_API_KEY
    value: your_api_key_here
    type: SECRET
  http_port: 8080

- name: frontend
  source_dir: /mindchat-frontend
  github:
    repo: your-username/mindchat
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NEXT_PUBLIC_API_URL
    value: ${backend.PUBLIC_URL}
  http_port: 3000
```

2. **Deploy**
```bash
doctl apps create --spec .do/app.yaml
```

## 🔒 Security Configuration

### SSL/TLS Setup

#### Using Let's Encrypt with Nginx

1. **Install Certbot**
```bash
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx
```

2. **Obtain Certificate**
```bash
sudo certbot --nginx -d yourdomain.com
```

3. **Auto-renewal**
```bash
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Nginx Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 📊 Monitoring and Logging

### Health Checks

```bash
# Backend health check
curl -f http://localhost:8000/ || exit 1

# Frontend health check
curl -f http://localhost:3000/ || exit 1
```

### Logging Configuration

```python
# backend/logging_config.py
import logging
import sys

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/mindchat/backend.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
```

### Monitoring with Prometheus

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'mindchat-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  - job_name: 'mindchat-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/metrics'
```

## 🔧 Maintenance

### Backup Procedures

```bash
# Backup ChromaDB data
docker-compose exec backend tar -czf /tmp/chromadb-backup.tar.gz /app/chromadb
docker cp $(docker-compose ps -q backend):/tmp/chromadb-backup.tar.gz ./backups/

# Backup environment configuration
cp .env ./backups/env-backup-$(date +%Y%m%d).env
```

### Update Procedures

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart services
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# Verify deployment
docker-compose ps
docker-compose logs -f
```

### Scaling

```yaml
# docker-compose.override.yml for scaling
version: '3.8'
services:
  backend:
    deploy:
      replicas: 3
  frontend:
    deploy:
      replicas: 2
```

## 🚨 Troubleshooting

### Common Issues

1. **Port Conflicts**
```bash
# Check port usage
netstat -tulpn | grep :8000
netstat -tulpn | grep :3000

# Kill processes using ports
sudo kill -9 $(sudo lsof -t -i:8000)
```

2. **Memory Issues**
```bash
# Check memory usage
docker stats
free -h

# Increase Docker memory limits
# Edit Docker Desktop settings or /etc/docker/daemon.json
```

3. **API Key Issues**
```bash
# Verify environment variables
docker-compose exec backend env | grep OPENAI
```

### Log Analysis

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Search logs for errors
docker-compose logs | grep ERROR
```

---

*This deployment guide covers the most common deployment scenarios. For specific requirements or custom configurations, please refer to the individual service documentation or contact the development team.*
