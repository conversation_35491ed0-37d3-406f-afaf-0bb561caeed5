# mindchat/backend/llm_providers/openai_compatible.py
from openai import OpenAI
from typing import List, Dict, Any # Import Any
from shared.config import OPENAI_API_KEY, OPENAI_API_BASE_URL
# No need to import Message schema here, as we receive formatted messages

# Initialize the OpenAI client only if API key is available
client = None
if OPENAI_API_KEY:
    client = OpenAI(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_API_BASE_URL,
    )

# Modify function signature to accept the pre-formatted messages list
def get_openai_compatible_response(model: str, messages: List[Dict[str, Any]]) -> str:
    """
    Calls an OpenAI-compatible API with the given pre-formatted messages list.
    """
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable is not set.")

    if not client:
        raise ValueError("OpenAI client is not initialized. Please check your API key configuration.")

    try:
        # Make the API call
        chat_completion = client.chat.completions.create(
            messages=messages, # Pass the pre-formatted messages
            model=model,
        )

        # Extract the assistant's response
        assistant_response = chat_completion.choices[0].message.content
        return assistant_response

    except Exception as e:
        print(f"Error calling OpenAI-compatible API: {e}")
        raise
