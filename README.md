# 🧠 MindChat

A modular AI chat interface with advanced memory management, data visualization, and extensible architecture. Built with modern web technologies for seamless AI interactions and conversation management.

![MindChat Demo](https://via.placeholder.com/800x400/4F46E5/FFFFFF?text=MindChat+Interface)

## ✨ Features

### 🤖 **Intelligent Chat System**
- Real-time AI conversations with context awareness
- Multiple AI provider support (OpenAI, custom APIs)
- Mock AI for testing and development
- Web search integration for up-to-date information

### 🧠 **Advanced Memory Management**
- Semantic search through conversation history
- Edit and delete stored memories
- Topic categorization and filtering
- Memory context integration in responses

### 📊 **Rich Data Visualization**
- Dynamic Chart.js generation (bar, line, pie, radar, matrix)
- Interactive Mermaid diagrams and flowcharts
- Chart export and sharing capabilities
- Real-time data visualization from AI responses

### 🎨 **Modern User Interface**
- Responsive design with mobile support
- Collapsible sidebar navigation
- Dark/light theme support (coming soon)
- Accessibility-first design principles

### 🔧 **Developer-Friendly**
- RESTful API with OpenAPI documentation
- Docker containerization for easy deployment
- Comprehensive test coverage
- Extensible plugin architecture

## 🚀 Quick Start

### Prerequisites
- **Node.js 18+** and **Python 3.9+** (for local development)
- **Git** for version control
- **OpenAI API Key** (optional, works with mock AI)

### Option 1: Deploy to Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/kaunghtut24/mindchat)

```bash
# 1. Fork this repository on GitHub
# 2. Connect your GitHub account to Vercel
# 3. Import the repository in Vercel
# 4. Add environment variables in Vercel dashboard
# 5. Deploy automatically!
```

### Option 2: Local Development

```bash
# Clone the repository
git clone https://github.com/kaunghtut24/mindchat.git
cd mindchat

# Backend setup
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --port 8000

# Frontend setup (new terminal)
cd mindchat-frontend
npm install
npm run dev

# Access: http://localhost:3000
```

### Option 3: Docker Deployment

```bash
# Configure environment variables
cp .env.example .env.local
# Edit .env.local with your API keys

# Start all services
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

## 📖 Documentation

| Document | Description |
|----------|-------------|
| [📋 Enhancement Plan](ENHANCEMENT_PLAN.md) | Comprehensive roadmap and feature planning |
| [🔌 API Documentation](API_DOCUMENTATION.md) | Complete API reference and examples |
| [🚀 Deployment Guide](DEPLOYMENT_GUIDE.md) | Production deployment instructions |
| [💻 Development Guide](DEVELOPMENT_GUIDE.md) | Developer setup and contribution guidelines |

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (ChromaDB)    │
│                 │    │                 │    │                 │
│ • Chat UI       │    │ • Chat API      │    │ • Vector Store  │
│ • Memory Browser│    │ • Memory API    │    │ • Embeddings    │
│ • Visualizations│    │ • AI Integration│    │ • Search Index  │
│ • Sidebar Nav   │    │ • Web Search    │    │ • Metadata      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

**Backend:**
- **FastAPI** - Modern Python web framework
- **ChromaDB** - Vector database for embeddings
- **Sentence Transformers** - Text embedding generation
- **OpenAI API** - AI language model integration

**Frontend:**
- **Next.js 15** - React framework with App Router
- **React 19** - Modern React with hooks
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - Beautiful UI components
- **Chart.js** - Interactive data visualization
- **Mermaid** - Diagram and flowchart generation

## 🎯 Current Status

### ✅ Fully Implemented Features
- [x] Real-time chat interface with AI responses
- [x] Memory management (view, edit, delete)
- [x] Semantic search through conversation history
- [x] Chart.js and Mermaid diagram generation
- [x] Responsive UI with sidebar navigation
- [x] RESTful API with comprehensive endpoints
- [x] Docker containerization
- [x] Mock AI for testing without API keys

### 🚧 In Development
- [ ] Advanced memory search with filters
- [ ] Settings management interface
- [ ] Data export/import functionality
- [ ] Topic management system
- [ ] Analytics dashboard

### 🔮 Planned Features
- [ ] Multi-user support with authentication
- [ ] Conversation threading
- [ ] Plugin system for extensibility
- [ ] Real-time collaboration
- [ ] Advanced analytics and insights

## 🧪 Testing

The application includes comprehensive test coverage:

```bash
# Backend tests
cd backend
pytest --cov=. --cov-report=html

# Frontend tests
cd mindchat-frontend
npm test -- --coverage
```

**Test Features:**
- Unit tests for all API endpoints
- Component testing for React components
- Integration tests for database operations
- End-to-end testing for critical user flows

## 🔧 Configuration

### Environment Variables

```bash
# AI Configuration
OPENAI_API_KEY=your_openai_api_key_here
LLM_MODEL_NAME=gpt-3.5-turbo
OPENAI_API_BASE_URL=https://api.openai.com/v1

# Search Configuration (Optional)
SERPAPI_API_KEY=your_serpapi_key_here
BRAVE_API_KEY=your_brave_api_key_here

# Performance Configuration
HF_HUB_DISABLE_SYMLINKS_WARNING=1
```

### API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/chat` | POST | Process chat messages with AI |
| `/api/memory` | GET | Retrieve stored memories |
| `/api/memory/{id}` | PUT/DELETE | Update/delete specific memory |
| `/api/memory/search` | POST | Semantic search through memories |
| `/api/memory/topics` | GET | List all conversation topics |

## 🤝 Contributing

We welcome contributions! Please see our [Development Guide](DEVELOPMENT_GUIDE.md) for detailed instructions.

### Quick Contribution Steps

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** following our coding standards
4. **Add tests** for new functionality
5. **Commit changes**: `git commit -m 'feat: add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Development Workflow

```bash
# Setup development environment
git clone <your-fork>
cd mindchat

# Backend development
cd backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Frontend development
cd mindchat-frontend
npm install
npm run dev

# Run tests
pytest  # Backend
npm test  # Frontend
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for providing powerful language models
- **ChromaDB** for efficient vector storage and search
- **Vercel** for Next.js and deployment platform
- **FastAPI** for the excellent Python web framework
- **Shadcn/ui** for beautiful, accessible UI components

## 📞 Support

- **Documentation**: Check our comprehensive guides above
- **Issues**: Report bugs or request features on GitHub Issues
- **Discussions**: Join community discussions on GitHub Discussions
- **API Reference**: Visit `/docs` endpoint when backend is running

---

**Built with ❤️ by the MindChat team**
