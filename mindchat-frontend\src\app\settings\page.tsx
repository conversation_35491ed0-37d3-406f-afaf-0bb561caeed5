"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Save, Key, Palette, Database, Download, Upload } from 'lucide-react';

interface Settings {
  apiKeys: {
    openai: string;
    serpapi: string;
    brave: string;
  };
  preferences: {
    theme: 'light' | 'dark' | 'system';
    autoSave: boolean;
    memoryRetention: number;
    responseLength: 'short' | 'medium' | 'long';
  };
  advanced: {
    maxMemoryItems: number;
    searchResultsLimit: number;
    enableWebSearch: boolean;
  };
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<Settings>({
    apiKeys: {
      openai: '',
      serpapi: '',
      brave: ''
    },
    preferences: {
      theme: 'system',
      autoSave: true,
      memoryRetention: 30,
      responseLength: 'medium'
    },
    advanced: {
      maxMemoryItems: 1000,
      searchResultsLimit: 5,
      enableWebSearch: false
    }
  });

  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('mindchat-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }, []);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Save to localStorage
      localStorage.setItem('mindchat-settings', JSON.stringify(settings));
      
      // In the future, this would also save to backend
      // await fetch('/api/settings', { method: 'PUT', body: JSON.stringify(settings) });
      
      setMessage({ type: 'success', text: 'Settings saved successfully!' });
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save settings. Please try again.' });
      setTimeout(() => setMessage(null), 3000);
    } finally {
      setSaving(false);
    }
  };

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'mindchat-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const imported = JSON.parse(e.target?.result as string);
          setSettings(prev => ({ ...prev, ...imported }));
          setMessage({ type: 'success', text: 'Settings imported successfully!' });
          setTimeout(() => setMessage(null), 3000);
        } catch (error) {
          setMessage({ type: 'error', text: 'Invalid settings file format.' });
          setTimeout(() => setMessage(null), 3000);
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-gray-600 mt-1">Manage your MindChat preferences and configuration</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportSettings}>
            <Download size={16} className="mr-2" />
            Export
          </Button>
          <label>
            <Button variant="outline" asChild>
              <span>
                <Upload size={16} className="mr-2" />
                Import
              </span>
            </Button>
            <input
              type="file"
              accept=".json"
              onChange={handleImportSettings}
              className="hidden"
            />
          </label>
          <Button onClick={handleSave} disabled={saving}>
            <Save size={16} className="mr-2" />
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.type === 'success' 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      <Tabs defaultValue="api-keys" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
        </TabsList>

        <TabsContent value="api-keys">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key size={20} className="mr-2" />
                API Keys
              </CardTitle>
              <CardDescription>
                Configure your API keys for AI providers and search services. Keys are stored locally and never sent to our servers.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="openai-key">OpenAI API Key</Label>
                <Input
                  id="openai-key"
                  type="password"
                  placeholder="sk-..."
                  value={settings.apiKeys.openai}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    apiKeys: { ...prev.apiKeys, openai: e.target.value }
                  }))}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Required for AI chat functionality. Get your key from <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI Platform</a>
                </p>
              </div>

              <div>
                <Label htmlFor="serpapi-key">SerpAPI Key (Optional)</Label>
                <Input
                  id="serpapi-key"
                  type="password"
                  placeholder="Your SerpAPI key..."
                  value={settings.apiKeys.serpapi}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    apiKeys: { ...prev.apiKeys, serpapi: e.target.value }
                  }))}
                />
                <p className="text-sm text-gray-500 mt-1">
                  For web search functionality. Get your key from <a href="https://serpapi.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">SerpAPI</a>
                </p>
              </div>

              <div>
                <Label htmlFor="brave-key">Brave Search API Key (Optional)</Label>
                <Input
                  id="brave-key"
                  type="password"
                  placeholder="Your Brave API key..."
                  value={settings.apiKeys.brave}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    apiKeys: { ...prev.apiKeys, brave: e.target.value }
                  }))}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Alternative web search provider. Get your key from <a href="https://brave.com/search/api/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Brave Search API</a>
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette size={20} className="mr-2" />
                User Preferences
              </CardTitle>
              <CardDescription>
                Customize your MindChat experience and behavior settings.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-save conversations</Label>
                  <p className="text-sm text-gray-500">Automatically save conversations to memory</p>
                </div>
                <Switch
                  checked={settings.preferences.autoSave}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    preferences: { ...prev.preferences, autoSave: checked }
                  }))}
                />
              </div>

              <div>
                <Label htmlFor="memory-retention">Memory Retention (days)</Label>
                <Input
                  id="memory-retention"
                  type="number"
                  min="1"
                  max="365"
                  value={settings.preferences.memoryRetention}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    preferences: { ...prev.preferences, memoryRetention: parseInt(e.target.value) || 30 }
                  }))}
                />
                <p className="text-sm text-gray-500 mt-1">
                  How long to keep conversation memories (1-365 days)
                </p>
              </div>

              <div>
                <Label htmlFor="response-length">Preferred Response Length</Label>
                <select
                  id="response-length"
                  className="w-full p-2 border rounded-md"
                  value={settings.preferences.responseLength}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    preferences: { ...prev.preferences, responseLength: e.target.value as 'short' | 'medium' | 'long' }
                  }))}
                >
                  <option value="short">Short (concise responses)</option>
                  <option value="medium">Medium (balanced responses)</option>
                  <option value="long">Long (detailed responses)</option>
                </select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database size={20} className="mr-2" />
                Advanced Settings
              </CardTitle>
              <CardDescription>
                Advanced configuration options for power users.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable Web Search</Label>
                  <p className="text-sm text-gray-500">Allow AI to search the web for current information</p>
                </div>
                <Switch
                  checked={settings.advanced.enableWebSearch}
                  onCheckedChange={(checked) => setSettings(prev => ({
                    ...prev,
                    advanced: { ...prev.advanced, enableWebSearch: checked }
                  }))}
                />
              </div>

              <div>
                <Label htmlFor="max-memory">Maximum Memory Items</Label>
                <Input
                  id="max-memory"
                  type="number"
                  min="100"
                  max="10000"
                  value={settings.advanced.maxMemoryItems}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    advanced: { ...prev.advanced, maxMemoryItems: parseInt(e.target.value) || 1000 }
                  }))}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Maximum number of conversation memories to store
                </p>
              </div>

              <div>
                <Label htmlFor="search-limit">Search Results Limit</Label>
                <Input
                  id="search-limit"
                  type="number"
                  min="1"
                  max="20"
                  value={settings.advanced.searchResultsLimit}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    advanced: { ...prev.advanced, searchResultsLimit: parseInt(e.target.value) || 5 }
                  }))}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Number of web search results to include in AI context
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about">
          <Card>
            <CardHeader>
              <CardTitle>About MindChat</CardTitle>
              <CardDescription>
                Information about the application and system status.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Version</Label>
                  <p className="text-sm text-gray-600">1.0.0</p>
                </div>
                <div>
                  <Label>Frontend</Label>
                  <p className="text-sm text-gray-600">Next.js 15.3.3</p>
                </div>
                <div>
                  <Label>Backend</Label>
                  <p className="text-sm text-gray-600">FastAPI + Python</p>
                </div>
                <div>
                  <Label>Database</Label>
                  <p className="text-sm text-gray-600">ChromaDB</p>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <Label>System Status</Label>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Backend Connection</span>
                    <span className="text-sm text-green-600">✓ Connected</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Memory Database</span>
                    <span className="text-sm text-green-600">✓ Active</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">AI Provider</span>
                    <span className="text-sm text-yellow-600">⚠ Mock Mode</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
