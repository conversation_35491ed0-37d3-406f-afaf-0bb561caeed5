# mindchat/backend/vercel_app.py
"""
Vercel-optimized entry point for MindChat backend.
This file adapts the FastAPI app for Vercel's serverless environment.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Import the main FastAPI app
from main import app

# Vercel expects the app to be available as 'app'
# The main.py already creates the FastAPI instance as 'app'

# Configure for serverless environment
app.title = "MindChat API"
app.description = "AI-powered chat interface with advanced memory management"
app.version = "1.0.0"

# Add serverless-specific middleware if needed
@app.middleware("http")
async def add_cors_header(request, call_next):
    response = await call_next(request)
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "*"
    return response

# Health check endpoint for Vercel
@app.get("/api/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "MindChat Backend",
        "environment": "vercel",
        "version": "1.0.0"
    }

# Export the app for Vercel
handler = app
