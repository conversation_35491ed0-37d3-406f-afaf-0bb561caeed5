# mindchat/backend/main.py
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware # Import CORSMiddleware
from api import chat, memory

app = FastAPI()

# --- CORS Configuration ---
# Define the origins that are allowed to make requests
origins = [
    "http://localhost",      # Allow requests from localhost without a specific port
    "http://localhost:3000", # Allow requests from your Next.js frontend
    # Add other origins if your frontend will be hosted elsewhere later
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,         # Allow specified origins
    allow_credentials=True,        # Allow cookies and authorization headers
    allow_methods=["*"],           # Allow all HTTP methods (GET, POST, PUT, DELETE, etc.)
    allow_headers=["*"],           # Allow all headers
)
# --- End CORS Configuration ---


# Import routers
from api import chat, memory, settings

# Include the routers
app.include_router(chat.router)
app.include_router(memory.router, prefix="/api")
app.include_router(settings.router, prefix="/api")

@app.get("/")
def read_root():
    return {"message": "MindChat Backend is running"}

# To run this server with environment variables and reload:
# Example (Windows PowerShell):
# $env:OPENAI_API_KEY="your_api_key"
# $env:LLM_MODEL_NAME="your_model_name"
# uvicorn main:app --reload --port 8000
