# mindchat/backend/api/settings.py
import os
import json
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from shared.config import OPENAI_API_KEY, SERPAPI_API_KEY, BRAVE_API_KEY, LLM_MODEL_NAME

router = APIRouter()

class APIKeysModel(BaseModel):
    openai: Optional[str] = None
    serpapi: Optional[str] = None
    brave: Optional[str] = None

class PreferencesModel(BaseModel):
    theme: str = "system"
    autoSave: bool = True
    memoryRetention: int = 30
    responseLength: str = "medium"

class AdvancedModel(BaseModel):
    maxMemoryItems: int = 1000
    searchResultsLimit: int = 5
    enableWebSearch: bool = False

class SettingsModel(BaseModel):
    apiKeys: APIKeysModel = APIKeysModel()
    preferences: PreferencesModel = PreferencesModel()
    advanced: AdvancedModel = AdvancedModel()

# In-memory storage for settings (in production, use a proper database)
_settings_storage: Dict[str, Any] = {}

@router.get("/settings")
async def get_settings():
    """Get current settings configuration."""
    try:
        # Get current environment variables (masked for security)
        current_settings = {
            "apiKeys": {
                "openai": "***" if OPENAI_API_KEY else "",
                "serpapi": "***" if SERPAPI_API_KEY else "",
                "brave": "***" if BRAVE_API_KEY else ""
            },
            "preferences": _settings_storage.get("preferences", {
                "theme": "system",
                "autoSave": True,
                "memoryRetention": 30,
                "responseLength": "medium"
            }),
            "advanced": _settings_storage.get("advanced", {
                "maxMemoryItems": 1000,
                "searchResultsLimit": 5,
                "enableWebSearch": bool(SERPAPI_API_KEY or BRAVE_API_KEY)
            })
        }
        return current_settings
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving settings: {str(e)}")

@router.put("/settings")
async def update_settings(settings: SettingsModel):
    """Update settings configuration."""
    try:
        # Store non-sensitive settings
        _settings_storage["preferences"] = settings.preferences.dict()
        _settings_storage["advanced"] = settings.advanced.dict()
        
        # Note: API keys are handled client-side for security
        # In production, you might want to encrypt and store them securely
        
        return {"message": "Settings updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating settings: {str(e)}")

@router.post("/settings/test-api-keys")
async def test_api_keys(api_keys: APIKeysModel):
    """Test API key validity without storing them."""
    results = {}
    
    # Test OpenAI API key
    if api_keys.openai:
        try:
            import openai
            client = openai.OpenAI(api_key=api_keys.openai)
            # Simple test call
            response = client.models.list()
            results["openai"] = {"status": "valid", "message": "API key is working"}
        except Exception as e:
            results["openai"] = {"status": "invalid", "message": str(e)}
    else:
        results["openai"] = {"status": "not_provided", "message": "No API key provided"}
    
    # Test SerpAPI key
    if api_keys.serpapi:
        try:
            import requests
            response = requests.get(
                "https://serpapi.com/account",
                params={"api_key": api_keys.serpapi}
            )
            if response.status_code == 200:
                results["serpapi"] = {"status": "valid", "message": "API key is working"}
            else:
                results["serpapi"] = {"status": "invalid", "message": "Invalid API key"}
        except Exception as e:
            results["serpapi"] = {"status": "error", "message": str(e)}
    else:
        results["serpapi"] = {"status": "not_provided", "message": "No API key provided"}
    
    # Test Brave API key
    if api_keys.brave:
        try:
            import requests
            response = requests.get(
                "https://api.search.brave.com/res/v1/web/search",
                params={"q": "test", "count": 1},
                headers={"X-Subscription-Token": api_keys.brave}
            )
            if response.status_code == 200:
                results["brave"] = {"status": "valid", "message": "API key is working"}
            else:
                results["brave"] = {"status": "invalid", "message": "Invalid API key"}
        except Exception as e:
            results["brave"] = {"status": "error", "message": str(e)}
    else:
        results["brave"] = {"status": "not_provided", "message": "No API key provided"}
    
    return results

@router.get("/settings/system-status")
async def get_system_status():
    """Get current system status and configuration."""
    try:
        status = {
            "backend": {
                "status": "operational",
                "version": "1.0.0",
                "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}"
            },
            "database": {
                "status": "operational",
                "type": "ChromaDB",
                "location": "local"
            },
            "ai_provider": {
                "status": "mock_mode" if not OPENAI_API_KEY else "configured",
                "provider": "OpenAI Compatible" if OPENAI_API_KEY else "Mock LLM",
                "model": LLM_MODEL_NAME
            },
            "search": {
                "status": "available" if (SERPAPI_API_KEY or BRAVE_API_KEY) else "not_configured",
                "providers": {
                    "serpapi": bool(SERPAPI_API_KEY),
                    "brave": bool(BRAVE_API_KEY)
                }
            },
            "features": {
                "memory_management": True,
                "visualization": True,
                "web_search": bool(SERPAPI_API_KEY or BRAVE_API_KEY),
                "real_ai": bool(OPENAI_API_KEY)
            }
        }
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving system status: {str(e)}")

@router.post("/settings/reset")
async def reset_settings():
    """Reset all settings to default values."""
    try:
        _settings_storage.clear()
        return {"message": "Settings reset to defaults successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resetting settings: {str(e)}")

@router.get("/settings/export")
async def export_settings():
    """Export current settings for backup."""
    try:
        export_data = {
            "preferences": _settings_storage.get("preferences", {}),
            "advanced": _settings_storage.get("advanced", {}),
            "export_timestamp": str(os.time.time()),
            "version": "1.0.0"
        }
        return export_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting settings: {str(e)}")

@router.post("/settings/import")
async def import_settings(settings_data: Dict[str, Any]):
    """Import settings from backup."""
    try:
        if "preferences" in settings_data:
            _settings_storage["preferences"] = settings_data["preferences"]
        if "advanced" in settings_data:
            _settings_storage["advanced"] = settings_data["advanced"]
        
        return {"message": "Settings imported successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error importing settings: {str(e)}")
