# mindchat/backend/memory/embedding_generator.py
from sentence_transformers import SentenceTransformer
from typing import List

# Initialize the sentence transformer model globally
# Using a small, fast model for demonstration
# Consider a larger model like 'all-mpnet-base-v2' for better performance later
model_name = 'all-MiniLM-L6-v2'
embedding_model = SentenceTransformer(model_name) # Model is initialized here

def generate_embedding(text: str) -> List[float]:
    """
    Generates a vector embedding for the given text.
    """
    # Access the globally initialized embedding_model directly
    # No need for 'global' or 'if embedding_model is None:' since it's initialized on module load
    embedding = embedding_model.encode(text)

    # Return as a list of floats for compatibility
    return embedding.tolist()

# Example Usage (for testing)
if __name__ == "__main__":
    text1 = "What is the capital of France?"
    text2 = "Paris is the capital of France."
    text3 = "How does photosynthesis work?"

    embedding1 = generate_embedding(text1)
    embedding2 = generate_embedding(text2)
    embedding3 = generate_embedding(text3)

    print(f"Embedding 1 length: {len(embedding1)}")
    print(f"Embedding 2 length: {len(embedding2)}")
    print(f"Embedding 3 length: {len(embedding3)}")

    # You can also calculate similarity if needed (e.g., using cosine similarity)
    from sentence_transformers.util import cos_sim
    similarity_1_2 = cos_sim(embedding1, embedding2)
    similarity_1_3 = cos_sim(embedding1, embedding3)

    print(f"Similarity between text1 and text2: {similarity_1_2.item()}")
    print(f"Similarity between text1 and text3: {similarity_1_3.item()}")

