﻿"use client"; // IMPORTANT: Keep this at the very top

import React, { useState, useRef, useEffect } from 'react';
import { Download } from 'lucide-react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    LineElement,
    PointElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
    RadialLinearScale,
} from 'chart.js';
import { Chart } from 'react-chartjs-2';

interface ChartRendererProps {
    config: any;
    messageId?: string;
}

export function ChartRenderer({ config, messageId }: ChartRendererProps) {
    const chartRef = useRef<ChartJS>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [chartError, setChartError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [zoomEnabled, setZoomEnabled] = useState(false);
    const [matrixEnabled, setMatrixEnabled] = useState(false);

    // Register Chart.js components and dynamically load optional plugins
    useEffect(() => {
        // Register core Chart.js components first
        ChartJS.register(
            CategoryScale,
            LinearScale,
            BarElement,
            LineElement,
            PointElement,
            Title,
            Tooltip,
            Legend,
            ArcElement,
            RadialLinearScale
        );

        // Dynamically load optional plugins
        const loadOptionalPlugins = async () => {
            if (typeof window !== 'undefined') {
                // Try to load zoom plugin
                try {
                    const zoomModule = await import('chartjs-plugin-zoom');
                    const zoomPluginInstance = zoomModule.default || zoomModule;
                    ChartJS.register(zoomPluginInstance);
                    setZoomEnabled(true);
                    console.log('Chart.js zoom plugin loaded and registered.');
                } catch (error) {
                    console.warn('Chart.js zoom plugin not available:', error);
                    setZoomEnabled(false);
                }

                // Try to load matrix chart plugin
                try {
                    const matrixModule = await import('chartjs-chart-matrix');
                    const { MatrixController, MatrixElement } = matrixModule;
                    ChartJS.register(MatrixController, MatrixElement);
                    setMatrixEnabled(true);
                    console.log('Chart.js matrix plugin loaded and registered.');
                } catch (error) {
                    console.warn('Chart.js matrix plugin not available:', error);
                    setMatrixEnabled(false);
                }

                setIsLoading(false);
            } else {
                setIsLoading(false);
            }
        };

        loadOptionalPlugins();

        // Cleanup function for charts
        return () => {
            if (chartRef.current) {
                chartRef.current.destroy();
            }
        };
    }, []);

    // Fix Chart.js v3+ configuration compatibility
    const normalizeChartConfig = (originalConfig: any) => {
        let normalizedConfig = { ...originalConfig };

        // Handle heatmap to matrix conversion
        if (normalizedConfig.type === 'heatmap' && !matrixEnabled) {
            console.warn('Heatmap type not supported, attempting to render as matrix');
            normalizedConfig.type = 'matrix';
        } else if (normalizedConfig.type === 'heatmap' && matrixEnabled) {
            // Convert heatmap to matrix if matrix is available
            normalizedConfig.type = 'matrix';
        }

        // Fix scales configuration for Chart.js v3+
        if (normalizedConfig.options?.scales) {
            const scales = normalizedConfig.options.scales;
            const newScales: any = {};

            // Handle old style scales (xAxes, yAxes)
            if (scales.xAxes || scales.yAxes) {
                if (scales.xAxes && scales.xAxes[0]) {
                    newScales.x = scales.xAxes[0];
                }
                if (scales.yAxes && scales.yAxes[0]) {
                    newScales.y = scales.yAxes[0];
                }
                // Remove old style scales
                delete scales.xAxes;
                delete scales.yAxes;
            } else {
                Object.assign(newScales, scales);
            }

            normalizedConfig.options.scales = newScales;
        }

        return normalizedConfig;
    };

    // Enhanced chart options for large datasets with v3+ compatibility
    const getOptimizedOptions = (originalConfig: any) => {
        const normalizedConfig = normalizeChartConfig(originalConfig);
        const datasetCount = normalizedConfig.data?.datasets?.length || 0;
        const dataPointCount = normalizedConfig.data?.datasets?.[0]?.data?.length || 0;
        const isLargeDataset = dataPointCount > 50 || datasetCount > 10;

        const baseOptions: any = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index' as const,
            },
            plugins: {
                legend: {
                    display: datasetCount <= 20,
                    position: isLargeDataset ? 'top' as const : 'right' as const,
                    labels: {
                        boxWidth: 12,
                        padding: 10,
                        usePointStyle: true,
                    }
                },
                tooltip: {
                    enabled: true,
                    callbacks: isLargeDataset ? {
                        title: (context: any) => context[0]?.label || '',
                        label: (context: any) => `${context.dataset.label}: ${context.parsed.y || context.parsed}`
                    } : undefined
                },
                // Only add zoom configuration if plugin is loaded and dataset is large
                ...(zoomEnabled && isLargeDataset ? {
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'xy' as const,
                        },
                        zoom: {
                            wheel: {
                                enabled: true,
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'xy' as const,
                        }
                    }
                } : {})
            },
            animation: {
                duration: isLargeDataset ? 0 : 1000,
            },
        };

        // Add scales for non-pie/doughnut/radar/matrix charts
        if (!['pie', 'doughnut', 'radar', 'matrix'].includes(normalizedConfig.type)) {
            baseOptions.scales = {
                x: {
                    display: true,
                    ticks: {
                        maxTicksLimit: isLargeDataset ? 10 : undefined,
                        maxRotation: 45,
                    }
                },
                y: {
                    display: true,
                    ticks: {
                        maxTicksLimit: isLargeDataset ? 8 : undefined,
                    },
                    beginAtZero: true,
                }
            };
        }

        // Merge with original options
        if (normalizedConfig.options) {
            return {
                ...baseOptions,
                ...normalizedConfig.options,
                plugins: {
                    ...baseOptions.plugins,
                    ...normalizedConfig.options.plugins,
                },
                scales: normalizedConfig.options.scales || baseOptions.scales,
            };
        }

        return baseOptions;
    };

    // Dynamic height calculation
    const getChartHeight = (config: any) => {
        const datasetCount = config.data?.datasets?.length || 0;
        const dataPointCount = config.data?.datasets?.[0]?.data?.length || 0;

        if (config.type === 'radar') return Math.min(500, Math.max(300, datasetCount * 50));
        if (['pie', 'doughnut', 'matrix'].includes(config.type)) return 400;
        if (dataPointCount > 100) return 600;
        if (dataPointCount > 50) return 500;
        if (datasetCount > 10) return Math.min(700, 300 + datasetCount * 20);

        return 400;
    };

    const downloadChartAsPng = () => {
        if (chartRef.current) {
            try {
                const url = chartRef.current.toBase64Image('image/png', 1.0);
                const link = document.createElement('a');
                link.download = `chart-${messageId || Date.now()}-${config.type}.png`;
                link.href = url;
                link.click();
            } catch (error) {
                console.error('Error downloading chart:', error);
                alert('Failed to download chart. Please try again.');
            }
        }
    };

    const downloadChartData = () => {
        try {
            const csvContent = generateCSV(config.data);
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = `chart-data-${messageId || Date.now()}.csv`;
            link.href = url;
            link.click();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading CSV:', error);
            alert('Failed to download data. Please try again.');
        }
    };

    const generateCSV = (data: any) => {
        if (!data.labels || !data.datasets) return '';

        const headers = ['Label', ...data.datasets.map((ds: any) => ds.label || 'Dataset')];
        const rows = data.labels.map((label: string, index: number) => [
            label,
            ...data.datasets.map((ds: any) => ds.data[index] || '')
        ]);

        return [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');
    };

    const handleFullscreen = () => {
        if (!chartRef.current) return;

        const modal = document.createElement('div');
        modal.id = 'chart-fullscreen-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            padding: 20px;
        `;

        const chartContainer = document.createElement('div');
        chartContainer.style.cssText = `
            width: 90vw;
            height: 80vh;
            background: white;
            border-radius: 8px;
            padding: 20px;
            position: relative;
            display: flex;
            flex-direction: column;
        `;

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '✕ Close Fullscreen';
        closeButton.style.cssText = `
            position: absolute;
            top: 10px;
            right: 15px;
            padding: 8px 14px;
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            color: #666;
            z-index: 10001;
        `;

        // Create header with chart title
        const header = document.createElement('div');
        header.style.cssText = `
            padding: 0 0 15px 0;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        `;

        const title = document.createElement('h2');
        title.textContent = config?.options?.plugins?.title?.text || 'Chart Fullscreen View';
        title.style.cssText = `
            margin: 0;
            color: #333;
            font-size: 20px;
            font-weight: 600;
        `;

        header.appendChild(title);

        // Create chart container for fullscreen
        const fullscreenChartDiv = document.createElement('div');
        fullscreenChartDiv.style.cssText = `
            flex: 1;
            position: relative;
            min-height: 0;
        `;

        const fullscreenCanvas = document.createElement('canvas');
        fullscreenChartDiv.appendChild(fullscreenCanvas);

        // Create new Chart.js instance for fullscreen
        const fullscreenOptions = {
            ...getOptimizedOptions(config),
            responsive: true,
            maintainAspectRatio: false,
        };

        let fullscreenChartInstance: ChartJS | null = null;

        try {
            const normalizedConfig = normalizeChartConfig(config);
            fullscreenChartInstance = new ChartJS(fullscreenCanvas, {
                type: normalizedConfig.type,
                data: normalizedConfig.data,
                options: fullscreenOptions
            });

            const cleanup = () => {
                if (fullscreenChartInstance) {
                    fullscreenChartInstance.destroy();
                }
                document.body.removeChild(modal);
                document.removeEventListener('keydown', handleEsc);
            };

            closeButton.onclick = cleanup;
            modal.onclick = (e) => {
                if (e.target === modal) cleanup();
            };

            const handleEsc = (e: KeyboardEvent) => {
                if (e.key === 'Escape') cleanup();
            };
            document.addEventListener('keydown', handleEsc);

        } catch (error) {
            console.error('Error creating fullscreen chart:', error);
            const errorDiv = document.createElement('div');
            errorDiv.innerHTML = `
                <p style="color: #666; text-align: center; padding: 40px;">
                    Unable to create fullscreen chart.<br>
                    <small>Error: ${error instanceof Error ? error.message : String(error)}</small>
                </p>
            `;
            fullscreenChartDiv.appendChild(errorDiv);

            // Ensure modal closes even if chart creation failed
            const cleanup = () => {
                document.body.removeChild(modal);
                document.removeEventListener('keydown', handleEsc);
            };
            closeButton.onclick = cleanup;
            const handleEsc = (e: KeyboardEvent) => {
                if (e.key === 'Escape') cleanup();
            };
            document.addEventListener('keydown', handleEsc);
        }

        chartContainer.appendChild(closeButton);
        chartContainer.appendChild(header);
        chartContainer.appendChild(fullscreenChartDiv);
        modal.appendChild(chartContainer);
        document.body.appendChild(modal);
    };

    // Manual zoom controls (alternative to plugin)
    const handleManualZoom = (action: 'in' | 'out' | 'reset') => {
        if (!chartRef.current) return;

        const chart = chartRef.current;

        try {
            // Use built-in Chart.js zoom methods provided by the plugin
            if (action === 'reset') {
                (chart as any).resetZoom();
            } else if (action === 'in') {
                (chart as any).zoom(1.1);
            } else if (action === 'out') {
                (chart as any).zoom(0.9);
            }
        } catch (error) {
            console.warn('Manual zoom not available:', error);
        }
    };

    // Get available chart types based on loaded plugins
    const getValidChartTypes = () => {
        const baseTypes = ['line', 'bar', 'pie', 'doughnut', 'radar', 'scatter', 'bubble', 'polarArea'];

        if (matrixEnabled) {
            baseTypes.push('matrix');
        }

        return baseTypes;
    };

    // Validate and normalize chart config
    useEffect(() => {
        try {
            if (!config || typeof config !== 'object') {
                setChartError('Invalid chart configuration');
                return;
            }

            if (!config.type || !config.data) {
                setChartError('Chart must have type and data properties');
                return;
            }

            const validTypes = getValidChartTypes();
            const normalizedConfig = normalizeChartConfig(config);

            // Check if the normalized type is valid
            if (!validTypes.includes(normalizedConfig.type)) {
                // Check if it's a missing plugin issue
                if (normalizedConfig.type === 'matrix' && !matrixEnabled) {
                    setChartError('Matrix charts require the chartjs-chart-matrix package. Please install it with: npm install chartjs-chart-matrix');
                } else if (config.type === 'heatmap') {
                    setChartError('Heatmap charts are not supported. Consider using matrix charts instead.');
                } else {
                    setChartError(`Unsupported chart type: ${normalizedConfig.type}. Available types: ${validTypes.join(', ')}`);
                }
                return;
            }

            // Validate data structure
            if (!normalizedConfig.data.datasets || !Array.isArray(normalizedConfig.data.datasets)) {
                setChartError('Chart data must have datasets array');
                return;
            }

            const dataPointCount = normalizedConfig.data?.datasets?.[0]?.data?.length || 0;
            const datasetCount = normalizedConfig.data?.datasets?.length || 0;

            if (dataPointCount > 1000) {
                console.warn(`Large dataset detected: ${dataPointCount} data points. Performance may be affected.`);
            }

            if (datasetCount > 50) {
                console.warn(`Many datasets detected: ${datasetCount} datasets. Consider grouping data.`);
            }

            // Log the normalized config for debugging
            console.log('Normalized chart config:', normalizedConfig);

            setChartError(null);
        } catch (error) {
            console.error('Error validating chart config:', error);
            setChartError(`Configuration error: ${(error as Error).message}`);
        }
    }, [config, matrixEnabled]);

    if (chartError) {
        return (
            <div className="my-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">Chart Error: {chartError}</p>
                <details className="mt-2">
                    <summary className="text-xs text-gray-600 cursor-pointer">Show Config</summary>
                    <pre className="text-xs text-gray-600 mt-2 overflow-x-auto max-h-40">
                        {JSON.stringify(config, null, 2)}
                    </pre>
                </details>
            </div>
        );
    }

    const normalizedConfig = normalizeChartConfig(config);
    const optimizedOptions = getOptimizedOptions(config);
    const chartHeight = getChartHeight(normalizedConfig);
    const dataPointCount = normalizedConfig.data?.datasets?.[0]?.data?.length || 0;
    const datasetCount = normalizedConfig.data?.datasets?.length || 0;
    const isLargeDataset = dataPointCount > 50 || datasetCount > 10;

    return (
        <div className="my-4">
            {(isLargeDataset) && (
                <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                    <p className="text-blue-700">
                        📊 Large Chart: {datasetCount} dataset(s), {dataPointCount} data points
                        {zoomEnabled ? " • Use mouse wheel to zoom, drag to pan" : ""}
                    </p>
                </div>
            )}

            {config.type === 'heatmap' && (
                <div className="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                    <p className="text-yellow-700">
                        ℹ️ Heatmap converted to matrix chart for better compatibility
                    </p>
                </div>
            )}

            <div
                ref={containerRef}
                className="bg-white p-4 rounded-lg border shadow-sm"
                style={{ minHeight: `${chartHeight}px` }}
            >
                {isLoading ? (
                    <div className="flex items-center justify-center h-32">
                        <p className="text-gray-500">Loading chart...</p>
                    </div>
                ) : (
                    <div style={{ height: `${chartHeight}px`, width: '100%' }}>
                        <Chart
                            ref={chartRef}
                            type={normalizedConfig.type}
                            data={normalizedConfig.data}
                            options={optimizedOptions}
                        />
                    </div>
                )}
            </div>

            <div className="flex justify-center items-center gap-2 mt-3 p-2 bg-gray-50 rounded flex-wrap">
                {/* Manual zoom controls for large datasets (if plugin available) */}
                {isLargeDataset && zoomEnabled && (
                    <>
                        <button
                            onClick={() => handleManualZoom('in')}
                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                            title="Zoom In"
                        >
                            🔍+ Zoom In
                        </button>

                        <button
                            onClick={() => handleManualZoom('out')}
                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                            title="Zoom Out"
                        >
                            🔍- Zoom Out
                        </button>

                        <button
                            onClick={() => handleManualZoom('reset')}
                            className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                            title="Reset Zoom"
                        >
                            ↻ Reset
                        </button>
                    </>
                )}

                <button
                    onClick={downloadChartAsPng}
                    className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                    title="Download Chart as PNG"
                >
                    <Download size={14} className="mr-1" /> PNG
                </button>

                {(dataPointCount > 0) && (
                    <button
                        onClick={downloadChartData}
                        className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                        title="Download Data as CSV"
                    >
                        📄 CSV
                    </button>
                )}

                <button
                    onClick={handleFullscreen}
                    className="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-800 bg-white border rounded focus:outline-none"
                    title="View Fullscreen"
                >
                    ⛶ Fullscreen
                </button>

                <span className="text-xs text-gray-500 px-2">
                    Type: {normalizedConfig.type} | Points: {dataPointCount}
                    {zoomEnabled ? " | Zoom: ✓" : ""}
                    {matrixEnabled ? " | Matrix: ✓" : ""}
                </span>
            </div>
        </div>
    );
}