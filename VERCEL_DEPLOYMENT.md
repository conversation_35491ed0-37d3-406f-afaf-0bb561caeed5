# 🚀 MindChat Vercel Deployment Guide

This guide will help you deploy MindChat to Vercel in under 10 minutes.

## 📋 Prerequisites

- GitHub account
- Vercel account (free at [vercel.com](https://vercel.com))
- OpenAI API key (optional, app works with mock AI)

## 🎯 One-Click Deployment

### Option 1: Deploy <PERSON><PERSON> (Fastest)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/kaunghtut24/mindchat)

1. Click the deploy button above
2. Connect your GitHub account to Vercel
3. Fork the repository to your GitHub
4. Configure environment variables (see below)
5. Deploy!

### Option 2: Manual Import

1. **Fork this repository** to your GitHub account
2. **Go to [Vercel Dashboard](https://vercel.com/dashboard)**
3. **Click "New Project"**
4. **Import your forked repository**
5. **Configure settings** (see below)
6. **Deploy!**

## ⚙️ Environment Variables Configuration

In your Vercel project dashboard, go to **Settings > Environment Variables** and add:

### Required for Real AI (Optional)
```
OPENAI_API_KEY = your_openai_api_key_here
LLM_MODEL_NAME = gpt-3.5-turbo
```

### Optional for Web Search
```
SERPAPI_API_KEY = your_serpapi_key_here
BRAVE_API_KEY = your_brave_api_key_here
```

### System Configuration
```
HF_HUB_DISABLE_SYMLINKS_WARNING = 1
PYTHONPATH = backend
```

## 🔧 Build Configuration

Vercel will automatically detect the configuration from `vercel.json`. No manual setup required!

### Automatic Configuration:
- ✅ **Frontend**: Next.js app in `mindchat-frontend/`
- ✅ **Backend**: Python FastAPI in `backend/`
- ✅ **Routing**: API routes automatically configured
- ✅ **Environment**: Variables injected automatically

## 🌐 Custom Domain (Optional)

1. Go to your project in Vercel dashboard
2. Click **Settings > Domains**
3. Add your custom domain
4. Follow DNS configuration instructions
5. SSL certificate is automatically provisioned

## 📊 Monitoring & Analytics

### Built-in Vercel Features:
- **Analytics**: Automatic page view tracking
- **Speed Insights**: Performance monitoring
- **Function Logs**: Backend API monitoring
- **Error Tracking**: Automatic error reporting

### Access Monitoring:
1. Go to your project dashboard
2. Click **Analytics** or **Functions** tab
3. Monitor performance and usage

## 🔄 Automatic Deployments

Every push to your main branch automatically triggers a new deployment:

1. **Push code** to GitHub
2. **Vercel detects** changes
3. **Builds and deploys** automatically
4. **Preview deployments** for pull requests

## 🐛 Troubleshooting

### Common Issues:

#### Build Fails
```bash
# Check build logs in Vercel dashboard
# Common fixes:
- Ensure all dependencies are in package.json
- Check Python requirements.txt
- Verify environment variables are set
```

#### API Routes Not Working
```bash
# Verify vercel.json configuration
# Check that backend/main.py exists
# Ensure PYTHONPATH is set to "backend"
```

#### Memory/Database Issues
```bash
# ChromaDB runs in-memory on Vercel
# Data is not persistent between deployments
# For production, consider external database
```

### Getting Help:
1. Check Vercel function logs
2. Review build logs in dashboard
3. Test API endpoints directly
4. Check environment variables

## 🚀 Production Optimization

### Performance Tips:
1. **Enable caching** in Vercel dashboard
2. **Use CDN** for static assets
3. **Optimize images** with Next.js Image component
4. **Monitor function execution time**

### Scaling Considerations:
- **Hobby Plan**: 100GB bandwidth, 100 function executions/day
- **Pro Plan**: 1TB bandwidth, unlimited functions
- **Enterprise**: Custom limits and SLA

## 📈 Monitoring Your App

### Key Metrics to Watch:
- **Function Duration**: Keep under 10s (hobby) / 60s (pro)
- **Memory Usage**: Monitor for optimization opportunities
- **Error Rate**: Track API failures
- **Response Time**: Monitor user experience

### Vercel Analytics:
```javascript
// Automatically included in Next.js
// View in Vercel dashboard > Analytics
```

## 🔐 Security Best Practices

### Environment Variables:
- ✅ Never commit API keys to Git
- ✅ Use Vercel environment variables
- ✅ Rotate keys regularly
- ✅ Use different keys for development/production

### CORS Configuration:
```javascript
// Automatically configured in vercel.json
// API routes accept requests from your domain only
```

## 📱 Mobile Optimization

The app is automatically optimized for mobile:
- ✅ Responsive design
- ✅ Touch-friendly interface
- ✅ Fast loading on mobile networks
- ✅ PWA capabilities ready

## 🎉 Success!

Once deployed, your MindChat app will be available at:
- **Production**: `https://your-app-name.vercel.app`
- **Custom Domain**: `https://your-domain.com` (if configured)

### Next Steps:
1. **Test all features** in production
2. **Configure custom domain** (optional)
3. **Set up monitoring** alerts
4. **Share with users** and gather feedback

## 📞 Support

- **Vercel Docs**: [vercel.com/docs](https://vercel.com/docs)
- **MindChat Issues**: [GitHub Issues](https://github.com/kaunghtut24/mindchat/issues)
- **Community**: [Vercel Discord](https://discord.gg/vercel)

---

**🎊 Congratulations! Your MindChat app is now live on Vercel!**
