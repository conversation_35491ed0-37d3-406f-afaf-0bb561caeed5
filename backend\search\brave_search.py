# mindchat/backend/search/brave_search.py
import os
import requests # Assuming requests is installed
from typing import Dict, List, Any

from shared.config import BRAVE_API_KEY

def perform_brave_search(query: str, num_results: int = 5) -> List[Dict[str, Any]]:
    """
    Performs a web search using Brave Search API.
    Returns a list of search results.
    """
    if not BRAVE_API_KEY:
        print("Brave API key is not configured.")
        return []

    # Brave Search API endpoint
    url = "https://api.search.brave.com/res/v1/web/search"

    # Headers require the API key
    headers = {
        "Accept": "application/json",
        "X-Subscription-Token": BRAVE_API_KEY
    }

    # Parameters for the search request
    params = {
        "q": query,
        "count": num_results # Number of results
        # Add other parameters as needed, e.g., country, language
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)

        results = response.json()

        # Brave Search API results structure can vary.
        # The main web search results are typically under results['web']['results']
        web_results = results.get("web", {}).get("results", [])

        # Format the results into a list of dictionaries
        formatted_results = []
        for res in web_results:
             formatted_results.append({
                 "title": res.get("title"),
                 "link": res.get("url"), # Brave API uses 'url' instead of 'link'
                 "snippet": res.get("description"), # Brave API uses 'description' instead of 'snippet'
                 # Add other fields if needed
             })

        print(f"Brave Search API search for '{query}' successful. Found {len(formatted_results)} web results.")
        return formatted_results

    except requests.exceptions.RequestException as e:
        print(f"Error during Brave Search API request for '{query}': {e}")
        return []
    except Exception as e:
        print(f"An unexpected error occurred during Brave Search for '{query}': {e}")
        return []

# Example Usage (for testing)
if __name__ == "__main__":
     # You would set the environment variable before running this script
     # export BRAVE_API_KEY="your_brave_api_key"

     if BRAVE_API_KEY:
         search_query = "current events in AI"
         search_results = perform_brave_search(search_query, num_results=3)
         print("\nBrave Search Results:")
         for i, res in enumerate(search_results):
             print(f"{i+1}. Title: {res.get('title')}")
             print(f"   Link: {res.get('link')}")
             print(f"   Snippet: {res.get('snippet')}")
             print("-" * 20)
     else:
         print("\nBrave API key not set. Cannot run test.")
